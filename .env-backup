DATABASE_URL=sqlite:///db.sqlite3
SECRET_KEY=django-insecure-k99^83^3e(mlk8my-@n29uc*-3l(*840)d(u&=^lpia(1#*o
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1
RECOVERY_TOKEN_HMAC_KEY=C-gH1kL2mN3p4R5s6T7v8W9xYz
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
CELERY_BROKER_URL=redis://localhost:6379/0
AWS_STORAGE_BUCKET_NAME=chatbook-backend-imports-dev-524752462460-us-west-2
AWS_S3_REGION_NAME=us-west-2
AWS_DEFAULT_REGION=us-west-2
AWS_SQS_FILE_PROCESSING_QUEUE_URL=https://sqs.us-west-2.amazonaws.com/524752462460/AwsCdkChatbookBackendStack-ProcessFileQueueFF19F3D8-B01E4oco0Qcu
AWS_PROFILE=sbx01
TIME_ZONE=UTC
AWS_ACCESS_KEY_ID=your-actual-access-key-id
AWS_SECRET_ACCESS_KEY=your-actual-secret-access-key
AWS_SES_FROM_EMAIL=<EMAIL>
AWS_SES_REGION_NAME=us-west-2
EMAIL_NOTIFICATIONS_ENABLED=true
AWS_SAMPLE_IMPORT_KEY=uploads/imports/sample/customer-import-sample.xlsx
HELCIM_BASE_URL=https://api.helcim.com/v2
HELCIM_API_TOKEN=atKiTa2NCTX1nou#wo%y2r-_!4S6jS*ED_LfOwuZRBqe#Wo!2D!lVmZA477NWL0t
HELCIM_TEST_MODE=True