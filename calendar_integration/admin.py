from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import CalendarConnection, CalendarEvent


@admin.register(CalendarConnection)
class CalendarConnectionAdmin(admin.ModelAdmin):
    """Admin interface for calendar connections"""
    
    list_display = [
        'user', 
        'provider', 
        'calendar_name',
        'connection_status_badge',
        'sync_enabled',
        'last_sync_at',
        'error_count',
        'created_at'
    ]
    
    def get_queryset(self, request):
        """Optimize queryset to prevent N+1 queries in admin list view"""
        queryset = super().get_queryset(request)
        return queryset.select_related('user', 'social_account')
    
    list_filter = [
        'provider',
        'connection_status',
        'sync_enabled',
        'created_at'
    ]
    
    search_fields = [
        'user__email',
        'user__first_name',
        'user__last_name',
        'calendar_name',
        'provider_calendar_id'
    ]
    
    readonly_fields = [
        'id',
        'created_at',
        'updated_at',
        'last_sync_at',
        'oauth_token_info'
    ]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'user', 'provider', 'provider_calendar_id', 'calendar_name')
        }),
        ('Connection Status', {
            'fields': ('connection_status', 'sync_enabled', 'last_sync_at', 'last_sync_status')
        }),
        ('Error Tracking', {
            'fields': ('last_error', 'error_count'),
            'classes': ('collapse',)
        }),
        ('OAuth Information', {
            'fields': ('social_account', 'oauth_token_info'),
            'classes': ('collapse',)
        }),
        ('Settings', {
            'fields': ('sync_settings',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def connection_status_badge(self, obj):
        """Display connection status with color coding"""
        status_colors = {
            'active': 'green',
            'inactive': 'gray',
            'error': 'red',
            'expired': 'orange'
        }
        color = status_colors.get(obj.connection_status, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_connection_status_display()
        )
    connection_status_badge.short_description = 'Status'
    connection_status_badge.admin_order_field = 'connection_status'
    
    def oauth_token_info(self, obj):
        """Display OAuth token information"""
        if not obj.social_account:
            return "No social account linked"
        
        info = []
        if obj.social_account.access_token:
            info.append("✅ Access Token")
        else:
            info.append("❌ Access Token")
            
        if obj.social_account.refresh_token:
            info.append("✅ Refresh Token")
        else:
            info.append("❌ Refresh Token")
            
        if obj.social_account.token_expiry:
            info.append(f"🕒 Expires: {obj.social_account.token_expiry}")
        
        return mark_safe("<br/>".join(info))
    oauth_token_info.short_description = 'OAuth Tokens'
    
    actions = ['mark_as_active', 'mark_as_inactive', 'reset_error_count']
    
    def mark_as_active(self, request, queryset):
        """Mark selected connections as active"""
        count = queryset.update(connection_status=CalendarConnection.StatusChoices.ACTIVE)
        self.message_user(request, f'{count} connections marked as active.')
    mark_as_active.short_description = "Mark selected connections as active"
    
    def mark_as_inactive(self, request, queryset):
        """Mark selected connections as inactive"""
        count = queryset.update(connection_status=CalendarConnection.StatusChoices.INACTIVE)
        self.message_user(request, f'{count} connections marked as inactive.')
    mark_as_inactive.short_description = "Mark selected connections as inactive"
    
    def reset_error_count(self, request, queryset):
        """Reset error count for selected connections"""
        count = queryset.update(error_count=0, last_error='')
        self.message_user(request, f'Error count reset for {count} connections.')
    reset_error_count.short_description = "Reset error count"


@admin.register(CalendarEvent)
class CalendarEventAdmin(admin.ModelAdmin):
    """Admin interface for calendar events"""
    
    list_display = [
        'appointment_id',
        'calendar_connection',
        'external_event_id',
        'sync_status_badge',
        'last_synced_at',
        'created_at'
    ]
    
    def get_queryset(self, request):
        """Optimize queryset to prevent N+1 queries in admin list view"""
        queryset = super().get_queryset(request)
        return queryset.select_related('calendar_connection', 'calendar_connection__user')
    
    list_filter = [
        'sync_status',
        'calendar_connection__provider',
        'calendar_connection__user',
        'created_at'
    ]
    
    search_fields = [
        'appointment_id',
        'external_event_id',
        'calendar_connection__user__email',
        'calendar_connection__calendar_name'
    ]
    
    readonly_fields = [
        'id',
        'event_data_hash',
        'created_at',
        'updated_at',
        'last_synced_at'
    ]
    
    fieldsets = (
        ('Event Information', {
            'fields': ('id', 'appointment_id', 'calendar_connection', 'external_event_id', 'external_calendar_id')
        }),
        ('Sync Status', {
            'fields': ('sync_status', 'last_synced_at', 'event_data_hash')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def sync_status_badge(self, obj):
        """Display sync status with color coding"""
        status_colors = {
            'synced': 'green',
            'pending': 'orange',
            'failed': 'red',
            'deleted': 'gray'
        }
        color = status_colors.get(obj.sync_status, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_sync_status_display()
        )
    sync_status_badge.short_description = 'Sync Status'
    sync_status_badge.admin_order_field = 'sync_status'