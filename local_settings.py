"""
Local development settings - use this for local development with SQLite.

Usage:
    python manage.py shell --settings=local_settings
    python manage.py runserver --settings=local_settings
"""

import os

# Clear database environment variables to force SQLite usage
os.environ.pop('DATABASE_HOST', None)
os.environ.pop('DATABASE_URL', None) 
os.environ.pop('DATABASE_NAME', None)
os.environ.pop('DATABASE_USER', None)
os.environ.pop('DATABASE_SECRET_ARN', None)

print("🧹 Cleared AWS database environment variables for local development")

# Now import the main settings
from settings import *

# Ensure we're using SQLite regardless
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

print("🗄️ Using local SQLite database for development")

# Additional development-specific settings  
DEBUG = True
ALLOWED_HOSTS = ['localhost', '127.0.0.1', '*.ngrok.io', '*.ngrok-free.app'] 

# Disable AWS services for local development
DEFAULT_FILE_STORAGE = 'django.core.files.storage.FileSystemStorage'
AWS_STORAGE_BUCKET_NAME = None

# Ignore migrations to avoid circular dependencies (if needed)
MIGRATION_MODULES = {
    'bookings': None,  # Tell Django to ignore bookings migrations
}