import re
import base64
import io
import uuid
from rest_framework import serializers
from .models import (
    CampaignType, Campaign, CampaignChannel, CampaignRecipient, 
    MarketingAnalytics
)
from business.models import BusinessCustomer
from aws_services.storage_service import storage_service


class CampaignTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = CampaignType
        fields = ['id', 'name', 'description', 'supports_offset', 'offset_unit', 
                 'min_offset', 'max_offset', 'is_system', 'created_at']
        read_only_fields = ['created_at']


class CampaignChannelSerializer(serializers.ModelSerializer):
    class Meta:
        model = CampaignChannel
        fields = ['id', 'channel_type', 'subject', 'content', 'raw_components',
                 'is_automatic', 'status', 'sent_time', 'credit_used']
        read_only_fields = ['credit_used', 'sent_time', 'status']
        
    def to_representation(self, instance):
        """确保raw_components字段正确序列化"""
        representation = super().to_representation(instance)
        
        # 确保content和raw_components字段被包含在响应中
        if hasattr(instance, 'content'):
            representation['content'] = instance.content
            
        if hasattr(instance, 'raw_components') and instance.raw_components is not None:
            representation['raw_components'] = instance.raw_components
            
        return representation


class CampaignRecipientSerializer(serializers.ModelSerializer):
    customer_email = serializers.SerializerMethodField()
    customer_name = serializers.SerializerMethodField()
    
    class Meta:
        model = CampaignRecipient
        fields = ['id', 'user', 'customer_email', 'customer_name', 
                 'sent', 'sent_time', 'opened', 'clicked']
        read_only_fields = ['sent', 'sent_time', 'opened', 'clicked']
    
    def get_customer_email(self, obj):
        return obj.user.email
    
    def get_customer_name(self, obj):
        user = obj.user
        return f"{user.first_name} {user.last_name}".strip() or user.email


class CampaignSerializer(serializers.ModelSerializer):
    channels = CampaignChannelSerializer(many=True, required=False)
    recipients_count = serializers.SerializerMethodField()
    recipients = serializers.SerializerMethodField()
    post_to_facebook = serializers.BooleanField(default=False, required=False)
    
    class Meta:
        model = Campaign
        fields = ['id', 'name', 'description', 'campaign_type', 
                 'status', 'scheduled_time', 'offset_value', 'active',
                 'created_at', 'channels', 'recipients', 'recipients_count', 
                 'sent_count', 'open_count', 'click_count', 'post_to_facebook']
        read_only_fields = ['created_at', 'sent_count', 'open_count', 'click_count']
    
    def get_recipients_count(self, obj):
        return obj.recipients.count()
    
    def get_recipients(self, obj):
        """Return list of recipient user IDs"""
        return [recipient.user_id for recipient in obj.recipients.all()]

    def _process_images_in_content(self, content, user):
        """Process base64 images in content and convert them to S3 URLs."""
        if not content:
            return content

        # Find all base64 images in content
        base64_pattern = r'data:image/[^;]+;base64,[^"\'>\s]+'
        base64_matches = re.findall(base64_pattern, content)

        print(f"🔍 [SERIALIZER] Processing content images for user {user.id}")
        print(f"🔍 [SERIALIZER] Found {len(base64_matches)} base64 images in content")

        processed_content = content

        # Process each base64 image
        for i, base64_data in enumerate(base64_matches, 1):
            try:
                print(f"📤 [SERIALIZER] Processing content image {i}/{len(base64_matches)}")

                # Parse base64 data URL
                if base64_data.startswith('data:'):
                    header, base64_content = base64_data.split(',', 1)
                    mime_type = header.split(';')[0].split(':')[1]

                    # Determine file extension
                    if 'jpeg' in mime_type or 'jpg' in mime_type:
                        ext = '.jpg'
                    elif 'png' in mime_type:
                        ext = '.png'
                    elif 'gif' in mime_type:
                        ext = '.gif'
                    elif 'webp' in mime_type:
                        ext = '.webp'
                    else:
                        ext = '.jpg'
                else:
                    base64_content = base64_data
                    mime_type = 'image/jpeg'
                    ext = '.jpg'

                # Generate filename
                filename = f'email_image_{uuid.uuid4().hex[:8]}{ext}'

                # Decode base64 data
                image_bytes = base64.b64decode(base64_content)
                image_file = io.BytesIO(image_bytes)

                # Generate S3 key
                s3_key = f"email_images/{user.id}/{uuid.uuid4().hex}/{filename}"

                # Upload to storage
                print(f"📤 [SERIALIZER] Uploading content image {i} with key: {s3_key}")
                storage_url = storage_service.upload_file(
                    file_obj=image_file,
                    key=s3_key,
                    content_type=mime_type
                )

                print(f"✅ [SERIALIZER] Content image {i} uploaded: {storage_url}")

                # Replace base64 data with storage URL in content
                processed_content = processed_content.replace(base64_data, storage_url)

            except Exception as e:
                # Log error but continue processing
                print(f"❌ [SERIALIZER] Error processing content image {i}: {str(e)}")
                continue

        return processed_content

    def _process_images_in_components(self, components, user):
        """Process base64 images in component data and convert them to S3 URLs."""
        if not components:
            return components

        print(f"🔍 [SERIALIZER] Processing component images for user {user.id}")
        print(f"🔍 [SERIALIZER] Total components to check: {len(components)}")

        processed_components = []
        component_image_count = 0

        for component in components:
            processed_component = component.copy()

            # Check if component has image source
            if 'imageSrc' in processed_component and processed_component['imageSrc']:
                image_src = processed_component['imageSrc']

                # If it's a base64 image, upload it to S3
                if image_src.startswith('data:image/'):
                    component_image_count += 1
                    try:
                        print(f"📤 [SERIALIZER] Processing component image {component_image_count} for component {processed_component.get('id', 'unknown')}")
                        # Parse base64 data URL
                        header, base64_content = image_src.split(',', 1)
                        mime_type = header.split(';')[0].split(':')[1]

                        # Determine file extension
                        if 'jpeg' in mime_type or 'jpg' in mime_type:
                            ext = '.jpg'
                        elif 'png' in mime_type:
                            ext = '.png'
                        elif 'gif' in mime_type:
                            ext = '.gif'
                        elif 'webp' in mime_type:
                            ext = '.webp'
                        else:
                            ext = '.jpg'

                        # Generate filename
                        filename = f'email_image_{uuid.uuid4().hex[:8]}{ext}'

                        # Decode base64 data
                        image_bytes = base64.b64decode(base64_content)
                        image_file = io.BytesIO(image_bytes)

                        # Generate S3 key
                        s3_key = f"email_images/{user.id}/{uuid.uuid4().hex}/{filename}"

                        # Upload to storage
                        print(f"📤 [SERIALIZER] Uploading component image {component_image_count} with key: {s3_key}")
                        storage_url = storage_service.upload_file(
                            file_obj=image_file,
                            key=s3_key,
                            content_type=mime_type
                        )

                        print(f"✅ [SERIALIZER] Component image {component_image_count} uploaded: {storage_url}")

                        # Replace base64 data with storage URL
                        processed_component['imageSrc'] = storage_url

                    except Exception as e:
                        # Log error but keep original image source
                        print(f"❌ [SERIALIZER] Error processing component image {component_image_count}: {str(e)}")

            processed_components.append(processed_component)

        print(f"🔍 [SERIALIZER] Component processing complete. Processed {component_image_count} images.")
        return processed_components
    
    def create(self, validated_data):
        channels_data = validated_data.pop('channels', [])
        recipients_ids = self.context['request'].data.get('recipients', [])
        post_to_facebook = validated_data.pop('post_to_facebook', False)

        campaign = Campaign.objects.create(**validated_data)

        # Set post_to_facebook field
        campaign.post_to_facebook = post_to_facebook
        campaign.save(update_fields=['post_to_facebook'])

        # Create channels with image processing
        user = self.context['request'].user
        print(f"🔍 [CAMPAIGN CREATE] Processing {len(channels_data)} channels for user {user.id}")

        for i, channel_data in enumerate(channels_data, 1):
            print(f"📧 [CAMPAIGN CREATE] Processing channel {i}: {channel_data.get('channel_type', 'unknown')}")

            # 检查是否有raw_components字段
            raw_components = None
            if 'raw_components' in channel_data:
                raw_components = channel_data.pop('raw_components')
                # Process images in components
                if raw_components:
                    print(f"🔍 [CAMPAIGN CREATE] Channel {i} has {len(raw_components)} components")
                    raw_components = self._process_images_in_components(raw_components, user)

            # Skip content image processing if components were already processed
            # This prevents duplicate uploads since frontend already processes images
            if 'content' in channel_data and channel_data['content']:
                print(f"🔍 [CAMPAIGN CREATE] Channel {i} has content ({len(channel_data['content'])} chars)")
                if not raw_components:
                    # Only process content images if no components were provided
                    print(f"🔍 [CAMPAIGN CREATE] No components found, processing content images")
                    channel_data['content'] = self._process_images_in_content(channel_data['content'], user)
                else:
                    print(f"🔍 [CAMPAIGN CREATE] Components already processed, skipping content image processing")

            channel = CampaignChannel.objects.create(campaign=campaign, **channel_data)

            # 单独设置raw_components字段，确保JSON数据正确保存
            if raw_components is not None:
                channel.raw_components = raw_components
                channel.save(update_fields=['raw_components'])

        # Add recipients
        for user_id in recipients_ids:
            try:
                CampaignRecipient.objects.create(campaign=campaign, user_id=user_id)
            except Exception:
                pass

        # Update statistics
        campaign.total_recipients = len(recipients_ids)
        campaign.save(update_fields=['total_recipients'])

        return campaign
    
    def update(self, instance, validated_data):
        channels_data = validated_data.pop('channels', None)
        post_to_facebook = validated_data.pop('post_to_facebook', None)
        recipients_ids = self.context['request'].data.get('recipients', [])

        # Update campaign fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        # Update post_to_facebook if provided
        if post_to_facebook is not None:
            instance.post_to_facebook = post_to_facebook

        instance.save()

        # Update channels if provided with image processing
        if channels_data is not None:
            instance.channels.all().delete()
            user = self.context['request'].user
            print(f"🔍 [CAMPAIGN UPDATE] Processing {len(channels_data)} channels for user {user.id}")

            for i, channel_data in enumerate(channels_data, 1):
                print(f"📧 [CAMPAIGN UPDATE] Processing channel {i}: {channel_data.get('channel_type', 'unknown')}")
                # 检查是否有raw_components字段
                raw_components = None
                if 'raw_components' in channel_data:
                    raw_components = channel_data.pop('raw_components')
                    # Process images in components
                    if raw_components:
                        print(f"🔍 [CAMPAIGN UPDATE] Channel {i} has {len(raw_components)} components")
                        raw_components = self._process_images_in_components(raw_components, user)

                # Skip content image processing if components were already processed
                # This prevents duplicate uploads since frontend already processes images
                if 'content' in channel_data and channel_data['content']:
                    print(f"🔍 [CAMPAIGN UPDATE] Channel {i} has content ({len(channel_data['content'])} chars)")
                    if not raw_components:
                        # Only process content images if no components were provided
                        print(f"🔍 [CAMPAIGN UPDATE] No components found, processing content images")
                        channel_data['content'] = self._process_images_in_content(channel_data['content'], user)
                    else:
                        print(f"🔍 [CAMPAIGN UPDATE] Components already processed, skipping content image processing")

                channel = CampaignChannel.objects.create(campaign=instance, **channel_data)

                # 单独设置raw_components字段，确保JSON数据正确保存
                if raw_components is not None:
                    channel.raw_components = raw_components
                    channel.save(update_fields=['raw_components'])

        # Update recipients if provided
        if recipients_ids:
            # Remove existing recipients
            instance.recipients.all().delete()

            # Add new recipients
            for user_id in recipients_ids:
                try:
                    CampaignRecipient.objects.create(campaign=instance, user_id=user_id)
                except Exception:
                    pass

            # Update statistics
            instance.total_recipients = len(recipients_ids)
            instance.save(update_fields=['total_recipients'])

        return instance


class MarketingAnalyticsSerializer(serializers.ModelSerializer):
    class Meta:
        model = MarketingAnalytics
        fields = ['id', 'campaign', 'roi', 'engagement_rate', 'bounce_rate', 
                  'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at'] 