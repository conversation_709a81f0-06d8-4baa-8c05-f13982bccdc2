import base64
import io
import uuid
import re
from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.authentication import TokenAuthentication, SessionAuthentication
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication
from django_filters.rest_framework import DjangoFilterBackend
from django.db import models
from django.db.models import Prefetch
from django.shortcuts import get_object_or_404
from django.core.files.uploadedfile import InMemoryUploadedFile

from .models import (
    CampaignType, Campaign, CampaignChannel, CampaignRecipient, 
    BusinessCreditUsage, MarketingAnalytics
)
from .serializers import (
    CampaignTypeSerializer, CampaignSerializer,
    CampaignChannelSerializer, CampaignRecipientSerializer,
    MarketingAnalyticsSerializer
)
from aws_services.email_service import email_service
from aws_services.storage_service import storage_service


class CampaignTypeViewSet(viewsets.ModelViewSet):
    """
    API endpoint for campaign types.
    
    list:
    Return a list of all campaign types.
    
    create:
    Create a new campaign type.
    
    retrieve:
    Return the given campaign type.
    
    update:
    Update the given campaign type.
    
    partial_update:
    Partially update the given campaign type.
    
    destroy:
    Delete the given campaign type.
    """
    serializer_class = CampaignTypeSerializer
    permission_classes = [IsAuthenticated]
    authentication_classes = [SessionAuthentication, JWTAuthentication, TokenAuthentication]
    filter_backends = [filters.SearchFilter]
    search_fields = ['name']
    
    def get_queryset(self):
        return CampaignType.objects.filter(
            models.Q(is_system=True)
        )


class CampaignViewSet(viewsets.ModelViewSet):
    """
    API endpoint for marketing campaigns.
    
    list:
    Return a list of all campaigns.
    
    create:
    Create a new campaign.
    
    retrieve:
    Return the given campaign.
    
    update:
    Update the given campaign.
    
    partial_update:
    Partially update the given campaign.
    
    destroy:
    Delete the given campaign.
    
    Additional actions:
    - recipients: Get list of campaign recipients
    - add_recipients: Add recipients to campaign
    - remove_recipients: Remove recipients from campaign
    - stats: Get campaign statistics by status
    - preview: Send a preview email for the campaign
    """
    serializer_class = CampaignSerializer
    permission_classes = [IsAuthenticated]
    authentication_classes = [SessionAuthentication, JWTAuthentication, TokenAuthentication]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    search_fields = ['name', 'description']
    filterset_fields = ['campaign_type', 'active', 'status']
    
    def get_queryset(self):
        queryset = Campaign.objects.all().prefetch_related(
            Prefetch(
                'channels',
                queryset=CampaignChannel.objects.all().order_by('id')
            )
        )
        
        # Filter by channel type
        channel_type = self.request.query_params.get('channel_type')
        if channel_type:
            queryset = queryset.filter(channels__channel_type=channel_type).distinct()
        
        # Filter by channel status
        channel_status = self.request.query_params.get('channel_status')
        if channel_status:
            queryset = queryset.filter(channels__status=channel_status).distinct()
            
        return queryset
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    def retrieve(self, request, *args, **kwargs):
        """
        重写retrieve方法，确保返回完整的channels数据，包括raw_components字段
        """
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get campaign statistics by status"""
        # Get all campaigns
        queryset = self.get_queryset()
        
        # Count total campaigns
        all_count = queryset.count()
        
        # Count campaigns by status
        sent_count = queryset.filter(status='completed').count()
        draft_count = queryset.filter(status='draft').count()
        queue_count = queryset.filter(status='scheduled').count()
        automated_count = queryset.filter(status='in_progress').count()
        archive_count = queryset.filter(status='cancelled').count()
        
        # Return statistics
        return Response({
            'all': all_count,
            'sent': sent_count,
            'draft': draft_count,
            'queue': queue_count,
            'automated': automated_count,
            'archive': archive_count
        })
    
    @action(detail=True, methods=['get'])
    def recipients(self, request, pk=None):
        """Get campaign recipients list"""
        campaign = self.get_object()
        recipients = campaign.recipients.all()
        
        page = self.paginate_queryset(recipients)
        if page is not None:
            serializer = CampaignRecipientSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = CampaignRecipientSerializer(recipients, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def add_recipients(self, request, pk=None):
        """Add recipients to campaign"""
        campaign = self.get_object()
        user_ids = request.data.get('recipients', [])
        
        if not user_ids:
            return Response(
                {"error": "No recipient IDs provided"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        added_count = 0
        for user_id in user_ids:
            # Check if already exists
            if not CampaignRecipient.objects.filter(
                campaign=campaign, 
                user_id=user_id
            ).exists():
                try:
                    CampaignRecipient.objects.create(
                        campaign=campaign, 
                        user_id=user_id
                    )
                    added_count += 1
                except Exception:
                    pass
        
        # Update statistics
        campaign.total_recipients = campaign.recipients.count()
        campaign.save(update_fields=['total_recipients'])
        
        return Response({"added": added_count})
    
    @action(detail=True, methods=['post'])
    def remove_recipients(self, request, pk=None):
        """Remove recipients from campaign"""
        campaign = self.get_object()
        user_ids = request.data.get('recipients', [])
        
        if not user_ids:
            return Response(
                {"error": "No recipient IDs provided"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Remove recipients
        removed = CampaignRecipient.objects.filter(
            campaign=campaign,
            user_id__in=user_ids
        ).delete()[0]
        
        # Update statistics
        campaign.total_recipients = campaign.recipients.count()
        campaign.save(update_fields=['total_recipients'])
        
        return Response({"removed": removed})
    
    @action(detail=True, methods=['post'])
    def preview(self, request, pk=None):
        """
        Send a preview email for a campaign.
        
        Request body:
        {
            "email_addresses": "<EMAIL>, <EMAIL>"  # Comma-separated list of emails
        }
        """
        campaign = self.get_object()
        
        # Get email addresses from request
        email_addresses = request.data.get('email_addresses', [])
        # string and list are both supported
        if isinstance(email_addresses, str):
            emails = [email.strip() for email in email_addresses.split(',') if email.strip()]
        elif isinstance(email_addresses, list):
            emails = [email.strip() for email in email_addresses if isinstance(email, str) and email.strip()]
        else:
            emails = []
        if not emails:
            return Response(
                {'error': 'No valid email addresses provided'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # SKIP business permission check (temporary)
            
            # Get email channel content
            email_channel = next(
                (channel for channel in campaign.channels.all() if channel.channel_type == 'email'),
                None
            )
            
            if not email_channel:
                return Response(
                    {'error': 'No email channel found for this campaign'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Get email content and subject
            html_content = email_channel.content
            subject = email_channel.subject or campaign.name or 'Campaign Preview'
            
            # Send preview email to all recipients
            email_service.send_campaign_preview(
                to_addresses=emails,
                subject=subject,
                html_content=html_content,
                campaign_name=campaign.name,
                text_content=None  # Optional plain text version
            )
            
            return Response({
                'message': f'Preview sent to {len(emails)} recipient(s)',
                'recipients': emails
            })
            
        except Exception as e:
            return Response(
                {'error': f'Failed to send preview: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def check_content(self, request, pk=None):
        """
        测试端点，用于检查是否正确返回content和raw_components字段
        """
        campaign = self.get_object()
        
        # 获取邮件渠道
        email_channel = next(
            (channel for channel in campaign.channels.all() if channel.channel_type == 'email'),
            None
        )
        
        if not email_channel:
            return Response({
                'error': 'No email channel found for this campaign'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 使用CampaignChannelSerializer序列化渠道数据
        serializer = CampaignChannelSerializer(email_channel)
        
        # 检查序列化后的数据是否包含content和raw_components字段
        data = serializer.data
        has_content = 'content' in data and data['content'] is not None
        has_raw_components = 'raw_components' in data and data['raw_components'] is not None
        
        return Response({
            'channel_id': email_channel.id,
            'has_content': has_content,
            'has_raw_components': has_raw_components,
            'content_length': len(data.get('content', '')) if has_content else 0,
            'raw_components_type': type(data.get('raw_components')).__name__ if has_raw_components else None,
            'full_data': data  # 返回完整的序列化数据
        })


class MarketingAnalyticsViewSet(viewsets.ModelViewSet):
    """
    API endpoint for marketing analytics.
    """
    serializer_class = MarketingAnalyticsSerializer
    permission_classes = [IsAuthenticated]
    authentication_classes = [SessionAuthentication, JWTAuthentication, TokenAuthentication]
    
    def get_queryset(self):
        return MarketingAnalytics.objects.all()


class MarketingCreditsAPIView(APIView):
    """
    API endpoint for marketing credits information.
    
    get:
    Return the marketing credits information for the current business.
    
    Returns:
    - email: Object containing email credits information (used and total)
    - text: Object containing text message credits information (used and total)
    """
    permission_classes = [IsAuthenticated]
    authentication_classes = [SessionAuthentication, JWTAuthentication, TokenAuthentication]
    
    def get(self, request, format=None):
        # Get the business associated with the current user
        try:
            # Assuming there's a relationship between user and business
            business = request.user.business_set.first()
            
            if not business:
                return Response(
                    {"error": "No business found for current user"}, 
                    status=status.HTTP_404_NOT_FOUND
                )
                
            # Get or create business credit usage record
            credit_usage, created = BusinessCreditUsage.objects.get_or_create(
                business=business,
                defaults={
                    'email_credits_used': 0,
                    'email_credit_limit': 1000,
                    'text_credits_used': 0,
                    'text_credit_limit': 0
                }
            )
            
            # Return formatted credit information
            return Response({
                'email': {
                    'used': credit_usage.email_credits_used,
                    'total': credit_usage.email_credit_limit
                },
                'text': {
                    'used': credit_usage.text_credits_used,
                    'total': credit_usage.text_credit_limit
                }
            })
            
        except Exception as e:
            # If an error occurs, return default values
            return Response({
                'email': {'used': 0, 'total': 1000},
                'text': {'used': 0, 'total': 0}
            })


class EmailImageUploadAPIView(APIView):
    """
    API endpoint for uploading images used in email campaigns.
    Converts base64 images to S3 URLs for use in email content.
    """
    permission_classes = [IsAuthenticated]
    authentication_classes = [SessionAuthentication, JWTAuthentication, TokenAuthentication]

    def post(self, request):
        """
        Upload a base64 image to S3 and return the URL.

        Expected payload:
        {
            "image_data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
            "filename": "image.jpg" (optional)
        }

        Returns:
        {
            "success": true,
            "url": "https://s3.amazonaws.com/bucket/path/to/image.jpg",
            "filename": "image.jpg"
        }
        """
        try:
            image_data = request.data.get('image_data')
            filename = request.data.get('filename', f'email_image_{uuid.uuid4().hex[:8]}.jpg')

            print(f"📤 [IMAGE UPLOAD] Starting upload for user {request.user.id}")
            print(f"📤 [IMAGE UPLOAD] Filename: {filename}")
            print(f"📤 [IMAGE UPLOAD] Data size: {len(image_data) if image_data else 0} characters")

            if not image_data:
                print("❌ [IMAGE UPLOAD] No image data provided")
                return Response({
                    'success': False,
                    'error': 'No image data provided'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Parse base64 data URL
            if image_data.startswith('data:'):
                # Extract MIME type and base64 data
                header, base64_data = image_data.split(',', 1)
                mime_type = header.split(';')[0].split(':')[1]

                # Determine file extension from MIME type
                if 'jpeg' in mime_type or 'jpg' in mime_type:
                    ext = '.jpg'
                elif 'png' in mime_type:
                    ext = '.png'
                elif 'gif' in mime_type:
                    ext = '.gif'
                elif 'webp' in mime_type:
                    ext = '.webp'
                else:
                    ext = '.jpg'  # Default to jpg

                # Ensure filename has correct extension
                if not filename.lower().endswith(ext):
                    filename = f"{filename.rsplit('.', 1)[0]}{ext}"
            else:
                # Assume it's raw base64 data
                base64_data = image_data
                mime_type = 'image/jpeg'
                ext = '.jpg'
                if not filename.lower().endswith(ext):
                    filename = f"{filename.rsplit('.', 1)[0]}{ext}"

            # Decode base64 data
            try:
                image_bytes = base64.b64decode(base64_data)
            except Exception as e:
                return Response({
                    'success': False,
                    'error': f'Invalid base64 data: {str(e)}'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Create file object
            image_file = io.BytesIO(image_bytes)

            # Generate S3 key for the image
            user_id = request.user.id
            s3_key = f"email_images/{user_id}/{uuid.uuid4().hex}/{filename}"

            print(f"📤 [IMAGE UPLOAD] Storage key: {s3_key}")
            print(f"📤 [IMAGE UPLOAD] Using {storage_service.backend_type} storage")

            # Upload to storage (S3 or local)
            try:
                storage_url = storage_service.upload_file(
                    file_obj=image_file,
                    key=s3_key,
                    content_type=mime_type
                )

                print(f"✅ [IMAGE UPLOAD] Upload successful: {storage_url}")

                # Get additional info about the storage backend
                backend_info = {'type': storage_service.backend_type}
                if storage_service.backend_type == 's3':
                    from config.aws import aws_config
                    backend_info['cloudfront_enabled'] = aws_config.use_cloudfront
                    if aws_config.use_cloudfront:
                        backend_info['cloudfront_domain'] = aws_config.cloudfront_domain

                return Response({
                    'success': True,
                    'url': storage_url,
                    'filename': filename,
                    'storage_key': s3_key,
                    'backend': backend_info
                })

            except Exception as e:
                print(f"❌ [IMAGE UPLOAD] Upload failed: {str(e)}")
                return Response({
                    'success': False,
                    'error': f'Failed to upload image: {str(e)}'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            return Response({
                'success': False,
                'error': f'Unexpected error: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class EmailContentImageProcessorAPIView(APIView):
    """
    API endpoint for processing email content and converting base64 images to S3 URLs.
    This is used when saving email campaigns to convert all embedded images.
    """
    permission_classes = [IsAuthenticated]
    authentication_classes = [SessionAuthentication, JWTAuthentication, TokenAuthentication]

    def post(self, request):
        """
        Process email content and convert base64 images to S3 URLs.

        Expected payload:
        {
            "content": "<html>...</html>",
            "components": [...] (optional, raw component data)
        }

        Returns:
        {
            "success": true,
            "processed_content": "<html>...</html>",
            "processed_components": [...],
            "uploaded_images": [
                {
                    "original_src": "data:image/jpeg;base64,...",
                    "new_url": "https://s3.amazonaws.com/...",
                    "filename": "image.jpg"
                }
            ]
        }
        """
        try:
            content = request.data.get('content', '')
            components = request.data.get('components', [])

            uploaded_images = []
            processed_content = content
            processed_components = components.copy() if components else []

            # Find all base64 images in content
            base64_pattern = r'data:image/[^;]+;base64,[^"\'>\s]+'
            base64_matches = re.findall(base64_pattern, content)

            # Process each base64 image found in content
            for base64_data in base64_matches:
                try:
                    # Upload image to S3
                    upload_response = self._upload_base64_image(base64_data, request.user)

                    if upload_response['success']:
                        # Replace base64 data with S3 URL in content
                        processed_content = processed_content.replace(base64_data, upload_response['url'])

                        uploaded_images.append({
                            'original_src': base64_data[:100] + '...',  # Truncate for response
                            'new_url': upload_response['url'],
                            'filename': upload_response['filename']
                        })

                except Exception as e:
                    print(f"Error processing image: {str(e)}")
                    continue

            # Process components if provided
            if processed_components:
                processed_components = self._process_components_images(processed_components, request.user, uploaded_images)

            return Response({
                'success': True,
                'processed_content': processed_content,
                'processed_components': processed_components,
                'uploaded_images': uploaded_images
            })

        except Exception as e:
            return Response({
                'success': False,
                'error': f'Failed to process email content: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _upload_base64_image(self, base64_data, user):
        """Helper method to upload a single base64 image to S3."""
        try:
            # Parse base64 data URL
            if base64_data.startswith('data:'):
                header, base64_content = base64_data.split(',', 1)
                mime_type = header.split(';')[0].split(':')[1]

                # Determine file extension
                if 'jpeg' in mime_type or 'jpg' in mime_type:
                    ext = '.jpg'
                elif 'png' in mime_type:
                    ext = '.png'
                elif 'gif' in mime_type:
                    ext = '.gif'
                elif 'webp' in mime_type:
                    ext = '.webp'
                else:
                    ext = '.jpg'
            else:
                base64_content = base64_data
                mime_type = 'image/jpeg'
                ext = '.jpg'

            # Generate filename
            filename = f'email_image_{uuid.uuid4().hex[:8]}{ext}'

            # Decode base64 data
            image_bytes = base64.b64decode(base64_content)
            image_file = io.BytesIO(image_bytes)

            # Generate S3 key
            s3_key = f"email_images/{user.id}/{uuid.uuid4().hex}/{filename}"

            # Upload to storage
            storage_url = storage_service.upload_file(
                file_obj=image_file,
                key=s3_key,
                content_type=mime_type
            )

            return {
                'success': True,
                'url': storage_url,
                'filename': filename,
                'storage_key': s3_key
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _process_components_images(self, components, user, uploaded_images):
        """Helper method to process images in component data."""
        processed_components = []

        for component in components:
            processed_component = component.copy()

            # Check if component has image source
            if 'imageSrc' in processed_component and processed_component['imageSrc']:
                image_src = processed_component['imageSrc']

                # If it's a base64 image, find the corresponding uploaded URL
                if image_src.startswith('data:image/'):
                    for uploaded_image in uploaded_images:
                        if image_src.startswith(uploaded_image['original_src'][:50]):  # Match beginning
                            processed_component['imageSrc'] = uploaded_image['new_url']
                            break

            processed_components.append(processed_component)

        return processed_components