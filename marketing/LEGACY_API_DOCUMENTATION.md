# Legacy Marketing API Documentation

This file contains the original Chinese API documentation that was in marketing/urls.py before the refactoring.

## Original API Structure (LEGACY)

The marketing module was originally a standalone Django app with the following endpoints:

### Chinese Documentation (Original)
```
# 创建路由 (Create Routes)
# 应用的URLs (App URLs)

# API文档说明： (API Documentation)
# GET /marketing/campaigns/ - 获取所有活动列表 (Get all campaigns list)
# POST /marketing/campaigns/ - 创建新活动 (Create new campaign)
# GET /marketing/campaigns/{id}/ - 获取单个活动详情 (Get single campaign details)
# PUT/PATCH /marketing/campaigns/{id}/ - 更新活动 (Update campaign)
# DELETE /marketing/campaigns/{id}/ - 删除活动 (Delete campaign)
# POST /marketing/campaigns/{id}/preview/ - 发送活动预览邮件 (Send campaign preview email)
# GET /marketing/campaigns/{id}/recipients/ - 获取活动收件人列表 (Get campaign recipients list)
# POST /marketing/campaigns/{id}/add-recipients/ - 添加收件人到活动 (Add recipients to campaign)
# POST /marketing/campaigns/{id}/remove-recipients/ - 从活动中移除收件人 (Remove recipients from campaign)
# GET /marketing/campaigns/stats/ - 获取活动统计信息 (Get campaign statistics)
# GET /marketing/campaign-types/ - 获取所有活动类型 (Get all campaign types)
# GET /marketing/credits/ - 获取账户营销积分信息 (Get account marketing credits info)
# POST /marketing/upload-image/ - 上传base64图片到S3并返回URL (Upload base64 image to S3 and return URL)
# POST /marketing/process-content/ - 处理邮件内容，将base64图片转换为S3 URL (Process email content, convert base64 images to S3 URL)
```

## Refactoring Notes

**Date:** 2025-08-15
**Refactored by:** AI Assistant
**Reason:** Moving marketing API to follow the same structure as other API modules under `api/v1/`

### Changes Made:
1. **Structure Change:**
   - Moved from `marketing/` app to `api/v1/marketing/`
   - Follows the same pattern as other API modules

2. **Files Moved:**
   - `marketing/views.py` → `api/v1/marketing/views.py`
   - `marketing/serializers.py` → `api/v1/marketing/serializers.py`
   - `marketing/urls.py` → `api/v1/marketing/urls.py`

3. **Import Updates:**
   - Updated imports to reference `marketing.models` instead of relative imports
   - Updated main `api/v1/urls.py` to use `api.v1.marketing.urls`

4. **Functionality Preserved:**
   - All original API endpoints maintained
   - All functionality preserved
   - Same authentication and permissions

### Migration Path:
- The marketing Django app (models, admin, etc.) remains in place
- Only the API views, serializers, and URLs were moved to follow the standard pattern
- Frontend code updated to use the same endpoints (no API changes)

### Legacy Files:
- Original marketing app files remain but are no longer used for API endpoints
- This documentation preserves the original Chinese comments for reference
