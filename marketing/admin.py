from django.contrib import admin
from .models import (
    CampaignType, Campaign, CampaignChannel, CampaignRecipient, 
    MarketingAnalytics
)


class CampaignChannelInline(admin.TabularInline):
    model = CampaignChannel
    extra = 1


class CampaignRecipientInline(admin.TabularInline):
    model = CampaignRecipient
    extra = 0
    fields = ('user', 'sent', 'sent_time', 'opened', 'clicked')
    readonly_fields = ('sent', 'sent_time', 'opened', 'clicked')


@admin.register(CampaignType)
class CampaignTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'supports_offset', 'is_system')
    list_filter = ('is_system', 'supports_offset')
    search_fields = ('name', 'description')


@admin.register(Campaign)
class CampaignAdmin(admin.ModelAdmin):
    list_display = ('name', 'campaign_type', 'status', 
                   'scheduled_time', 'active', 'total_recipients', 'sent_count')
    list_filter = ('status', 'active', 'campaign_type')
    search_fields = ('name', 'description')
    readonly_fields = ('created_by', 'created_at', 'updated_at', 
                      'total_recipients', 'sent_count', 'open_count', 'click_count')
    inlines = [CampaignChannelInline, CampaignRecipientInline]
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('campaign_type')


@admin.register(MarketingAnalytics)
class MarketingAnalyticsAdmin(admin.ModelAdmin):
    list_display = ('campaign', 'emails_sent', 'emails_opened', 'emails_clicked', 'conversions', 'revenue', 'created_at')
    search_fields = ('campaign__name',)
    readonly_fields = ('created_at', 'updated_at') 