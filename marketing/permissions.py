from rest_framework.permissions import BasePermission


class HasMarketingPermission(BasePermission):
    """
    Check if user has marketing permission.
    """
    def has_permission(self, request, view):
        user = request.user
        
        # Check authentication
        if not user.is_authenticated:
            return False
            
        # For simplicity in this implementation, allow all authenticated users
        return True
            
    def has_object_permission(self, request, view, obj):
        """
        Check if user has permission to access specific object.
        """
        user = request.user
        
        # For simplicity in this implementation, allow all authenticated users
        # In a real implementation, you would check if the user has permission to access the object
        return True 