from django.db import models
from django.conf import settings
from django.utils import timezone
from django.db.models import F
from rest_framework.permissions import BasePermission


class CampaignType(models.Model):
    """
    Marketing campaign types such as birthday marketing, lost customer recall, etc.
    """
    OFFSET_UNIT_CHOICES = [
        ('days', 'Days'),
        ('weeks', 'Weeks'),
        ('months', 'Months'),
    ]

    name = models.CharField(max_length=50)
    description = models.TextField(blank=True)
    
    # Support date offset (e.g., 7 days before birthday)
    supports_offset = models.BooleanField(default=False)
    offset_unit = models.CharField(max_length=10, choices=OFFSET_UNIT_CHOICES, null=True, blank=True)
    min_offset = models.IntegerField(null=True, blank=True)
    max_offset = models.IntegerField(null=True, blank=True)
    
    # System preset type
    is_system = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.name


class Campaign(models.Model):
    """
    Main marketing campaign model
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('scheduled', 'Scheduled'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    name = models.CharField(max_length=255)
    campaign_type = models.ForeignKey(CampaignType, on_delete=models.SET_NULL, null=True, related_name='campaigns')
    
    # Basic information
    description = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    
    # Time related
    scheduled_time = models.DateTimeField()
    offset_value = models.IntegerField(null=True, blank=True)
    
    # Campaign control
    active = models.BooleanField(default=True)
    post_to_facebook = models.BooleanField(default=False)
    
    # Statistics (reserved fields)
    total_recipients = models.IntegerField(default=0)
    sent_count = models.IntegerField(default=0)
    open_count = models.IntegerField(default=0)
    click_count = models.IntegerField(default=0)
    conversion_count = models.IntegerField(default=0)
    
    # Metadata
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='created_campaigns')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return self.name
    
    def update_stats(self):
        """Update campaign statistics"""
        self.total_recipients = self.recipients.count()
        self.sent_count = self.recipients.filter(sent=True).count()
        self.open_count = self.recipients.filter(opened=True).count()
        self.click_count = self.recipients.filter(clicked=True).count()
        self.save(update_fields=['total_recipients', 'sent_count', 'open_count', 'click_count'])


class MarketingAnalytics(models.Model):
    """Marketing analytics model for tracking campaign performance"""
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, related_name='analytics')
    date = models.DateField(auto_now_add=True)
    emails_sent = models.IntegerField(default=0)
    emails_opened = models.IntegerField(default=0)
    emails_clicked = models.IntegerField(default=0)
    conversions = models.IntegerField(default=0)
    revenue = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = 'Marketing Analytics'
        verbose_name_plural = 'Marketing Analytics'
        unique_together = ('campaign', 'date')
    
    def __str__(self):
        return f"Analytics for {self.campaign.name} - {self.date}"


class CampaignTemplate(models.Model):
    """
    Reusable marketing templates
    """
    CHANNEL_TYPE_CHOICES = [
        ('email', 'Email'),
        ('text', 'Text Message'),
    ]
    
    business = models.ForeignKey('business.Business', on_delete=models.CASCADE, related_name='campaign_templates')
    name = models.CharField(max_length=255, verbose_name='Template Name')
    description = models.TextField(blank=True, verbose_name='Description')
    
    channel_type = models.CharField(max_length=10, choices=CHANNEL_TYPE_CHOICES, verbose_name='Channel Type')
    subject = models.CharField(max_length=255, blank=True, verbose_name='Subject')
    content = models.TextField(verbose_name='Content')
    
    # System preset
    is_system = models.BooleanField(default=False, verbose_name='System Preset')
    
    # Metadata
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='created_templates', verbose_name='Created By')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='Created At')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='Updated At')
    
    class Meta:
        verbose_name = 'Campaign Template'
        verbose_name_plural = 'Campaign Templates'
    
    def __str__(self):
        return f"{self.name} - {self.business.name}"


class CampaignChannel(models.Model):
    """
    Marketing campaign channels (email/text)
    """
    CHANNEL_TYPE_CHOICES = [
        ('email', 'Email'),
        ('text', 'Text Message'),
    ]
    
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('ready', 'Ready'),
        ('queued', 'Queued'),
        ('sending', 'Sending'),
        ('sent', 'Sent'),
        ('failed', 'Failed'),
    ]
    
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, related_name='channels')
    channel_type = models.CharField(max_length=10, choices=CHANNEL_TYPE_CHOICES, verbose_name='Channel Type')
    
    # Content
    subject = models.CharField(max_length=255, blank=True, verbose_name='Subject')
    content = models.TextField(blank=True, verbose_name='Content')
    raw_components = models.JSONField(null=True, blank=True, verbose_name='Raw Components')
    template = models.ForeignKey(CampaignTemplate, on_delete=models.SET_NULL, null=True, blank=True, related_name='campaign_channels', verbose_name='Template')
    
    # Send control
    is_automatic = models.BooleanField(default=False, verbose_name='Automatic Send')
    status = models.CharField(max_length=50, choices=STATUS_CHOICES, default='draft', verbose_name='Status')
    
    # Send information
    sent_time = models.DateTimeField(null=True, blank=True, verbose_name='Sent Time')
    
    # Statistics (reserved fields)
    credit_used = models.IntegerField(default=0, verbose_name='Credits Used')
    revenue = models.DecimalField(max_digits=10, decimal_places=2, default=0.00, verbose_name='Revenue Generated')
    appointment_count = models.IntegerField(default=0, verbose_name='Appointment Count')
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='Created At')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='Updated At')
    
    class Meta:
        unique_together = ('campaign', 'channel_type')
        verbose_name = 'Campaign Channel'
        verbose_name_plural = 'Campaign Channels'
    
    def __str__(self):
        return f"{self.get_channel_type_display()} - {self.campaign.name}"
    
    def get_tracking_url(self, recipient_id, link_id=None):
        """Generate tracking URL"""
        from django.urls import reverse
        base_url = settings.BASE_URL.rstrip('/')
        
        if link_id:
            return f"{base_url}{reverse('campaign-tracking-link', args=[self.id, recipient_id, link_id])}"
        else:
            return f"{base_url}{reverse('campaign-tracking-pixel', args=[self.id, recipient_id])}"


class CampaignLink(models.Model):
    """
    Links in marketing campaigns for click tracking
    """
    campaign_channel = models.ForeignKey(CampaignChannel, on_delete=models.CASCADE, related_name='links')
    original_url = models.URLField(verbose_name='Original URL')
    link_id = models.CharField(max_length=50, unique=True, verbose_name='Link ID')
    
    # Statistics
    click_count = models.IntegerField(default=0, verbose_name='Click Count')
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='Created At')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='Updated At')
    
    class Meta:
        verbose_name = 'Campaign Link'
        verbose_name_plural = 'Campaign Links'
    
    def __str__(self):
        return f"{self.link_id} - {self.campaign_channel.campaign.name}"


class CampaignRecipient(models.Model):
    """
    Marketing campaign recipients
    """
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, related_name='recipients')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='campaign_recipients')
    
    # Matching information
    matched_by_rule = models.BooleanField(default=False)
    
    # Send status
    sent = models.BooleanField(default=False)
    sent_time = models.DateTimeField(null=True, blank=True)
    
    # Tracking information (reserved fields)
    opened = models.BooleanField(default=False)
    opened_time = models.DateTimeField(null=True, blank=True)
    opened_count = models.IntegerField(default=0)
    
    clicked = models.BooleanField(default=False)
    clicked_time = models.DateTimeField(null=True, blank=True)
    clicked_count = models.IntegerField(default=0)
    
    # Error information
    error = models.TextField(blank=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ('campaign', 'user')
        
    def __str__(self):
        return f"{self.user.email} - {self.campaign.name}"
    
    def mark_opened(self):
        """Mark as opened"""
        now = timezone.now()
        if not self.opened:
            self.opened = True
            self.opened_time = now
        
        self.opened_count += 1
        self.save(update_fields=['opened', 'opened_time', 'opened_count', 'updated_at'])
        
        # Update campaign statistics
        self.campaign.open_count = F('open_count') + (1 if self.opened_count == 1 else 0)
        self.campaign.save(update_fields=['open_count'])
    
    def mark_clicked(self):
        """Mark as clicked"""
        now = timezone.now()
        if not self.clicked:
            self.clicked = True
            self.clicked_time = now
        
        self.clicked_count += 1
        self.save(update_fields=['clicked', 'clicked_time', 'clicked_count', 'updated_at'])
        
        # Update campaign statistics
        self.campaign.click_count = F('click_count') + (1 if self.clicked_count == 1 else 0)
        self.campaign.save(update_fields=['click_count'])


class CampaignTrackingEvent(models.Model):
    """
    Tracking events for marketing campaigns (open, click, etc.)
    """
    EVENT_TYPE_CHOICES = [
        ('open', 'Open'),
        ('click', 'Click'),
        ('bounce', 'Bounce'),
        ('unsubscribe', 'Unsubscribe'),
        ('conversion', 'Conversion'),
    ]
    
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, related_name='tracking_events')
    recipient = models.ForeignKey(CampaignRecipient, on_delete=models.CASCADE, related_name='tracking_events')
    event_type = models.CharField(max_length=20, choices=EVENT_TYPE_CHOICES, verbose_name='Event Type')
    
    # Event data
    event_time = models.DateTimeField(auto_now_add=True, verbose_name='Event Time')
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name='IP Address')
    user_agent = models.TextField(blank=True, verbose_name='User Agent')
    
    # Click event specific data
    link = models.ForeignKey(CampaignLink, on_delete=models.SET_NULL, null=True, blank=True, related_name='events', verbose_name='Link')
    
    # Conversion event specific data
    conversion_type = models.CharField(max_length=50, blank=True, verbose_name='Conversion Type')
    conversion_value = models.DecimalField(max_digits=10, decimal_places=2, default=0.00, verbose_name='Conversion Value')
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='Created At')
    
    class Meta:
        ordering = ['-event_time']
        verbose_name = 'Tracking Event'
        verbose_name_plural = 'Tracking Events'
    
    def __str__(self):
        return f"{self.get_event_type_display()} - {self.recipient.user.email}"


class BusinessCreditUsage(models.Model):
    """
    Business marketing credit usage
    """
    business = models.OneToOneField('business.Business', on_delete=models.CASCADE, related_name='credit_usage')
    
    # Email credits
    email_credits_used = models.IntegerField(default=0, verbose_name='Email Credits Used')
    email_credit_limit = models.IntegerField(default=1000, verbose_name='Email Credit Limit')
    
    # Text message credits
    text_credits_used = models.IntegerField(default=0, verbose_name='Text Credits Used')
    text_credit_limit = models.IntegerField(default=0, verbose_name='Text Credit Limit')
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='Created At')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='Updated At')
    
    class Meta:
        verbose_name = 'Business Credit Usage'
        verbose_name_plural = 'Business Credit Usage'
    
    def __str__(self):
        return f"{self.business.name} Credits"
    
    def has_sufficient_credits(self, channel_type, count=1):
        """Check if there are sufficient credits"""
        if channel_type == 'email':
            return self.email_credits_used + count <= self.email_credit_limit
        elif channel_type == 'text':
            return self.text_credits_used + count <= self.text_credit_limit
        return False
    
    def use_credits(self, channel_type, count=1):
        """Use credits"""
        if not self.has_sufficient_credits(channel_type, count):
            return False
            
        if channel_type == 'email':
            self.email_credits_used += count
        elif channel_type == 'text':
            self.text_credits_used += count
            
        self.save(update_fields=[f'{channel_type}_credits_used', 'updated_at'])
        return True


class HasCampaignPermission(BasePermission):
    """
    Check if user has campaign permission.
    """
    def has_permission(self, request, view):
        # Check if user is authenticated
        if not request.user.is_authenticated:
            return False
            
        # Check if user has marketing role or permission
        # This depends on your specific permission system
        return request.user.has_perm('marketing.view_campaign')
    
    def has_object_permission(self, request, view, obj):
        # Check if user is the creator of the campaign
        if obj.created_by == request.user:
            return True
            
        # Or check if user has specific permissions
        return request.user.has_perm('marketing.change_campaign')