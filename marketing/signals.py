from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import CampaignRecipient, Campaign


@receiver(post_save, sender=CampaignRecipient)
def update_campaign_stats_on_recipient_change(sender, instance, created, **kwargs):
    """
    Update campaign statistics when a recipient is added or modified
    """
    if instance.campaign:
        instance.campaign.update_stats() 