"""
Django settings for chatbook-backend project.

Generated by 'django-admin startproject' using Django 5.2.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""
import os
from pathlib import Path
from datetime import timedelta
from dotenv import load_dotenv

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent

# Load environment variables from .env file
load_dotenv(BASE_DIR / '.env', override=True)


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-k99^83^3e($2mlk8my-@n29uc*-3l(*840)d(u&=^lpia(1#*o'

# Use a different key for recovery token HMAC operations
RECOVERY_TOKEN_HMAC_KEY = 'C$t3D-gH1kL2mN3p4R5s6T7v8W9xYz$AbCdEfGhIjK'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

# ALLOWED_HOSTS - Environment-aware configuration
# For local development: restrict to localhost
# For containers/production: allow internal AWS IPs and configured hosts
if os.getenv('SERVICE_TYPE') in ['api', 'worker'] or os.getenv('AWS_EXECUTION_ENV', '').startswith('AWS_ECS'):
    # Container environment - allow all hosts (secured by AWS VPC/Security Groups)
    ALLOWED_HOSTS = ['*']
    
    # Optionally, you can be more specific if you know your domain/load balancer
    # ALLOWED_HOSTS = [
    #     'localhost',
    #     '127.0.0.1',
    #     'your-domain.com',
    #     'your-load-balancer-dns.us-west-2.elb.amazonaws.com',
    # ]
    
    # Allow additional hosts from environment variable
    additional_hosts = os.getenv('DJANGO_ALLOWED_HOSTS', '')
    if additional_hosts and ALLOWED_HOSTS != ['*']:
        ALLOWED_HOSTS.extend([h.strip() for h in additional_hosts.split(',') if h.strip()])
else:
    # Local development - restrict to localhost only
    ALLOWED_HOSTS = ['localhost', '127.0.0.1', '*.ngrok.io', '*.ngrok-free.app', 'b61dd41593b1.ngrok-free.app']

# AWS Configuration
AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY')
AWS_SESSION_TOKEN = os.getenv('AWS_SESSION_TOKEN')  # 用于临时凭证
AWS_PROFILE = os.getenv('AWS_PROFILE')

# AWS SES Configuration for email sending
AWS_SES_REGION_NAME = os.getenv('AWS_SES_REGION_NAME', 'us-west-2')
AWS_SES_SENDER_EMAIL = os.getenv('AWS_SES_SENDER_EMAIL', '<EMAIL>')
AWS_SES_SENDER_NAME = os.getenv('AWS_SES_SENDER_NAME', 'Chatbook')
AWS_SES_CONFIGURATION_SET = os.getenv('AWS_SES_CONFIGURATION_SET', '')  # Optional

# AWS Configuration for file processing (no lambda dependencies)
AWS_SQS_FILE_PROCESSING_QUEUE_URL = os.getenv('AWS_SQS_FILE_PROCESSING_QUEUE_URL')

# Use environment variables for all AWS settings to support both local development and production
AWS_STORAGE_BUCKET_NAME = os.getenv('AWS_STORAGE_BUCKET_NAME')
AWS_S3_REGION_NAME = os.getenv('AWS_S3_REGION_NAME', 'us-west-2')
AWS_DEFAULT_REGION = os.getenv('AWS_DEFAULT_REGION', 'us-west-2')

# AWS Credentials Configuration
# For local development: use .env file or AWS CLI profiles
# For production (ECS): use IAM roles attached to the task/instance
# boto3 will automatically use the credential chain:
# 1. Environment variables (AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY)
# 2. AWS credentials file (~/.aws/credentials)
# 3. IAM roles (for EC2/ECS/Lambda)
# 4. AWS CLI profiles (only for local development)

# Only use AWS_PROFILE for local development if needed
# In production, this should be None to use IAM roles
AWS_PROFILE = os.getenv('AWS_PROFILE', None)

# SNS topic for push notifications
AWS_SNS_TOPIC_ARN = os.getenv('AWS_SNS_TOPIC_ARN')
SNS_TOPIC_ARN = os.getenv('SNS_TOPIC_ARN')  # Alternative environment variable name for compatibility

# Django-storages S3 configuration
DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
AWS_S3_CUSTOM_DOMAIN = None 
AWS_S3_FILE_OVERWRITE = False 
AWS_DEFAULT_ACL = None 
AWS_S3_OBJECT_PARAMETERS = {
    'CacheControl': 'max-age=86400',  # Cache for 1 day
}
AWS_QUERYSTRING_AUTH = False  # Don't add query string authentication
AWS_S3_SECURE_URLS = True  # Use HTTPS URLs
AWS_S3_USE_SSL = True  # Use SSL for S3 connections

# Application definition

INSTALLED_APPS = [
    # Third-party apps
    'jazzmin',  # Must be before django.contrib.admin
    'apps.files',
    'rest_framework',
    'rest_framework_simplejwt',
    'rest_framework_simplejwt.token_blacklist',  # Add token blacklist app
    'allauth',
    'allauth.account',
    'allauth.socialaccount',
    'allauth.socialaccount.providers.google',
    'corsheaders',
    'django_filters',
    'django_otp',
    'django_otp.plugins.otp_totp',
    'django_otp.plugins.otp_static',
    'axes',
    
    # Django core apps
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.sites',  # Required for django-allauth
    
    # Local apps
    'accounts.apps.AccountsConfig',  # User & Company Management
    'business.apps.BusinessConfig',  # Business management
    'forms.apps.FormsConfig',  # Forms and signatures management
    'services',
    'employees',
    'customers.apps.CustomersConfig',  # Customer management
    'appointments',  # Consolidated appointment model
    'waitlist.apps.WaitlistConfig',  # Waitlist management
    'payments',
    'notifications.apps.NotificationsConfig',  # Push notification management
    'calendar_integration.apps.CalendarConfig',  # Calendar integration management
    'reports',
    'api',
    'marketing.apps.MarketingConfig',  # Marketing campaign management

    # s3 storage
    'storages',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django_otp.middleware.OTPMiddleware',  # django-otp for MFA
    'appointments.middleware.AppointmentHistoryMiddleware',  # Capture user context for history tracking
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'allauth.account.middleware.AccountMiddleware',
    'axes.middleware.AxesMiddleware',  # django-axes for rate limiting and brute force protection
    'api.middleware.error_handling.RequestResponseLoggingMiddleware',  # Request/response logging
    'api.middleware.error_handling.ErrorHandlingMiddleware',  # Custom error handling
]

ROOT_URLCONF = 'urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [
            BASE_DIR / 'templates',  # Project-level templates
            BASE_DIR / 'templates/admin',  # Custom admin templates
        ],
        'APP_DIRS': True,  # Look for templates in app directories
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'wsgi.application'

def get_database_password():
    """Retrieve database password from AWS Secrets Manager"""
    secret_arn = os.getenv('DATABASE_SECRET_ARN')
    if not secret_arn:
        return None
    
    try:
        import boto3
        import json
        
        # Use IAM role in ECS, profile locally
        if os.getenv('AWS_EXECUTION_ENV', '').startswith('AWS_ECS'):
            # Running in ECS - use IAM role (no profile needed)
            client = boto3.client('secretsmanager', region_name='us-west-2')
        else:
            # Running locally - use profile
            aws_profile = os.getenv('AWS_PROFILE', 'sbx01')
            session = boto3.Session(profile_name=aws_profile)
            client = session.client('secretsmanager', region_name='us-west-2')
        
        response = client.get_secret_value(SecretId=secret_arn)
        credentials = json.loads(response['SecretString'])
        
        return credentials.get('password')
        
    except Exception as e:
        print(f"❌ Failed to retrieve database password: {str(e)}")
        return None

# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

if os.getenv('DATABASE_HOST') or os.getenv('DATABASE_URL'):
    db_password = get_database_password()
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': os.getenv('DATABASE_NAME', 'chatbook'),
            'USER': os.getenv('DATABASE_USER', 'chatbook_admin'),
            'PASSWORD': db_password,  # Use password from Secrets Manager
            'HOST': os.getenv('DATABASE_HOST'),
            'PORT': os.getenv('DATABASE_PORT', '5432'),
            'OPTIONS': {
                'sslmode': 'require',
                'connect_timeout': 60,
            },
            'CONN_MAX_AGE': 60,  # Connection pooling for 60 seconds
            'CONN_HEALTH_CHECKS': True,  # Enable connection health checks
        }
    }
    print(f"🐘 Using PostgreSQL database: {os.getenv('DATABASE_HOST')}")
else:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
        }
    }


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = "America/Los_Angeles"

USE_I18N = True

USE_TZ = True

# SQS Worker configuration (replaced Celery)
# Workers run as separate ECS services that auto-scale based on queue depth


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = 'static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'

# Additional locations of static files
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# Media files (Uploads)
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# File upload settings
FILE_UPLOAD_MAX_MEMORY_SIZE = 50 * 1024 * 1024  # 50MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 50 * 1024 * 1024  # 50MB
FILE_UPLOAD_PERMISSIONS = 0o644

# Local storage configuration for development
LOCAL_MEDIA_ROOT = os.getenv('LOCAL_MEDIA_ROOT', 'media/uploads')
USE_LOCAL_STORAGE = os.getenv('USE_LOCAL_STORAGE', 'false').lower() == 'true'
BASE_URL = os.getenv('BASE_URL', 'http://localhost:8000')

# CloudFront configuration
AWS_CLOUDFRONT_DOMAIN = os.getenv('AWS_CLOUDFRONT_DOMAIN')
USE_CLOUDFRONT = bool(AWS_CLOUDFRONT_DOMAIN)

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Auth settings
AUTH_USER_MODEL = 'accounts.User'
AUTHENTICATION_BACKENDS = [
    'axes.backends.AxesStandaloneBackend',
    'accounts.backends.EmailOrPhoneBackend',
    'allauth.account.auth_backends.AuthenticationBackend',
]

# Sites framework settings
SITE_ID = 1

# REST Framework settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.TokenAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'api.permissions.PublicReadOnlyPrivateWrite',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
    'EXCEPTION_HANDLER': 'api.exceptions.custom_exception_handler',
    # Datetime format configuration - use ISO 8601 with timezone offset only
    'DATETIME_FORMAT': '%Y-%m-%dT%H:%M:%S%z',
    'DATETIME_INPUT_FORMATS': [
        '%Y-%m-%dT%H:%M:%S%z',  # 2025-06-11T17:00:00+00:00
    ],
}

# JWT Settings
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=60),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=14),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,  # Changed to True to enable blacklisting
    'UPDATE_LAST_LOGIN': True,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,
    'AUTH_HEADER_TYPES': ('Bearer',),
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',
}

# CORS settings - very permissive for development
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True
CORS_EXPOSE_HEADERS = ['Content-Type', 'X-CSRFToken']
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]

# Only use this in development
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
]

# CSRF settings for different environments
if os.getenv('SERVICE_TYPE') == 'api' or os.getenv('AWS_EXECUTION_ENV', '').startswith('AWS_ECS'):
    # In ECS/container environment
    CSRF_TRUSTED_ORIGINS = [
        'http://*',  # Trust all HTTP origins in development ECS
        'https://*', # Trust all HTTPS origins in production ECS
    ]
    # Disable CSRF for API endpoints in container environment
    CSRF_COOKIE_HTTPONLY = False
else:
    # Local development - keep existing CORS settings
    pass

# Django-allauth settings
ACCOUNT_EMAIL_VERIFICATION = 'none'  # Disable email verification
ACCOUNT_LOGIN_METHODS = {'email'}  # Replace ACCOUNT_AUTHENTICATION_METHOD
ACCOUNT_SIGNUP_FIELDS = ['email*', 'password1*', 'password2*']  # Replace EMAIL_REQUIRED and USERNAME_REQUIRED
ACCOUNT_UNIQUE_EMAIL = True

# OTP settings
OTP_EXPIRY_MINUTES = 1440  # 24 hours
OTP_LENGTH = 6

# Twilio settings for SMS OTP
TWILIO_ACCOUNT_SID = ''  # Add your Twilio Account SID
TWILIO_AUTH_TOKEN = ''   # Add your Twilio Auth Token
TWILIO_PHONE_NUMBER = '' # Add your Twilio phone number

# Rate Limiting Settings
LOGIN_ATTEMPT_LIMIT = 5  # Maximum number of login attempts
LOGIN_ATTEMPT_WINDOW = 300  # Window in seconds (5 minutes)
OTP_ATTEMPT_LIMIT = 3  # Maximum number of OTP verification attempts
OTP_ATTEMPT_WINDOW = 300  # Window in seconds (5 minutes)

# Environment-aware cache configuration
if os.getenv('AWS_EXECUTION_ENV', '').startswith('AWS_ECS'):
    # In ECS: Use database cache instead of Redis
    CACHES = {
        "default": {
            "BACKEND": "django.core.cache.backends.db.DatabaseCache",
            "LOCATION": "django_cache_table",
        }
    }
    # Use database sessions in ECS
    SESSION_ENGINE = "django.contrib.sessions.backends.db"
else:
    # Local development: Use Redis
    CACHES = {
        "default": {
            "BACKEND": "django_redis.cache.RedisCache", 
            "LOCATION": "redis://127.0.0.1:6379/1",
            "OPTIONS": {
                "CLIENT_CLASS": "django_redis.client.DefaultClient",
            }
        }
    }
    # Use cache for session backend in local development
    SESSION_ENGINE = "django.contrib.sessions.backends.cache"
    SESSION_CACHE_ALIAS = "default"

# Use cache for session backend
SESSION_ENGINE = "django.contrib.sessions.backends.cache"
SESSION_CACHE_ALIAS = "default"

# Social Authentication Settings
SOCIALACCOUNT_PROVIDERS = {
    'google': {
        'APP': {
            'client_id': '',
            'secret': '',
            'key': ''
        },
        'SCOPE': [
            'profile',
            'email',
        ],
    }
}

# MFA Settings
MFA_ISSUER_NAME = 'ChatBook'
MFA_VALIDITY_WINDOW = 300  # 5 minutes
MFA_CODE_LENGTH = 6

# Recovery token settings
RECOVERY_TOKEN_EXPIRY_HOURS = 24

# Session Settings
SESSION_COOKIE_AGE = 1209600  # 2 weeks
# SESSION_COOKIE_SECURE = True requires HTTPS, but we're testing on http://localhost:8000. This means session cookies aren't being sent, so the OAuth state is lost.
SESSION_COOKIE_SECURE = not DEBUG
CSRF_COOKIE_SECURE = not DEBUG

# Admin site customization
ADMIN_SITE_HEADER = "Chatbook Administration"
ADMIN_SITE_TITLE = "Chatbook Admin"
ADMIN_INDEX_TITLE = "Site Administration"

# Axes Configuration
AXES_FAILURE_LIMIT = 5
AXES_LOCK_OUT_AT_FAILURE = True
AXES_COOLOFF_TIME = 1  # 1 hour
AXES_RESET_ON_SUCCESS = True

# Jazzmin settings for admin customization
JAZZMIN_SETTINGS = {
    # Title visible in the admin interface
    "site_title": "Chatbook Admin",
    # Title displayed on the login screen
    "site_header": "Chatbook",
    # Title on the brand in top left (text beside the logo)
    "site_brand": "Chatbook Administration",
    # Logo to use for the site
    "site_logo": None,
    # CSS classes that are applied to the logo
    "site_logo_classes": "img-circle",
    # Logo to display in login form
    "login_logo": None,
    # Logo shown in the UI when collapsed
    "login_logo_dark": None,
    
    # UI Customization
    "custom_css": None,
    "custom_js": None,
    "show_ui_builder": False,
    
    # Links to put on the user dropdown menu
    "usermenu_links": [],
    
    # Icons for apps/models - use FontAwesome icons
    "icons": {
        "accounts": "fas fa-users-cog",
        "accounts.user": "fas fa-user",
        "business.business": "fas fa-building",
        "employees.employee": "fas fa-id-card",
        "customers.customer": "fas fa-user-friends",
        "business.businesscustomer": "fas fa-handshake",
        "appointments.appointment": "fas fa-calendar-alt",
        "services.service": "fas fa-concierge-bell",
        "services.stylegroup": "fas fa-tags",
        "services.addonsuggestionsrule": "fas fa-lightbulb",
        "services.lashservicesuggestionsrule": "fas fa-route",
        "waitlist.waitlistentry": "fas fa-list-alt",
        "waitlist.preferredwindow": "fas fa-clock",
    },
    
    # Order of apps and models in the sidebar menu
    "order_with_respect_to": [
        "accounts",
        "business",
        "employees",
        "customers",
        "services",
        "appointments",
        "waitlist",
    ],
    
    # Group models together in the admin index page
    "related_modal_active": True,
    
    # Custom app/model grouping
    "custom_links": {},
    
    # Configure menu ordering and grouping
    "show_sidebar": True,
    "navigation_expanded": True,
    "hide_apps": [],
    "hide_models": [],
    
    # Custom admin menu
    "default_icon_parents": "fas fa-folder",
    "default_icon_children": "fas fa-circle",
    
    # Customize app names
    "custom_links_in_order": True,
    "show_apps": True,
    "show_apps_expanded": True,
    
    # Custom menu items
    "menu": [
        {
            "name": "User Management",
            "icon": "fas fa-users-cog",
            "models": [
                "accounts.user",
                "auth.group",
            ]
        },
        {
            "name": "Business",
            "icon": "fas fa-building",
            "models": [
                "business.business",
            ]
        },
        {
            "name": "People",
            "icon": "fas fa-users",
            "models": [
                "employees.employee",
                "customers.customer",
                "business.businesscustomer",
            ]
        },
        {
            "name": "Services",
            "icon": "fas fa-concierge-bell",
            "models": [
                "services.service",
                "services.servicecategory",
                "services.stylegroup",
                "services.addon",
                "services.addonsuggestionsrule",
                "services.lashservicesuggestionsrule",
                "services.employeeservice",
            ]
        },
        {
            "name": "Appointments",
            "icon": "fas fa-calendar-alt",
            "models": [
                "appointments.appointment",
                "appointments.recurringpattern",
                "appointments.appointmentservice",
                "appointments.appointmentaddon",
            ]
        },
        {
            "name": "Waitlist",
            "icon": "fas fa-list-alt",
            "models": [
                "waitlist.waitlistentry",
                "waitlist.preferredwindow",
            ]
        },
        {
            "name": "System",
            "icon": "fas fa-cogs",
            "models": [
                "django_otp.totpdevice",
                "sites.site",
                "socialaccount.socialapp",
            ]
        },
    ],
}

# Add admin site app groups for better organization
JAZZMIN_UI_TWEAKS = {
    "navbar_small_text": False,
    "footer_small_text": False,
    "body_small_text": False,
    "brand_small_text": False,
    "brand_colour": "navbar-primary",
    "accent": "accent-primary",
    "navbar": "navbar-dark",
    "no_navbar_border": False,
    "navbar_fixed": True,
    "layout_boxed": False,
    "footer_fixed": False,
    "sidebar_fixed": True,
    "sidebar": "sidebar-dark-primary",
    "sidebar_nav_small_text": False,
    "sidebar_disable_expand": False,
    "sidebar_nav_child_indent": True,
    "sidebar_nav_compact_style": False,
    "sidebar_nav_legacy_style": False,
    "sidebar_nav_flat_style": False,
    "theme": "simplex",
    "dark_mode_theme": None,
    "button_classes": {
        "primary": "btn-primary",
        "secondary": "btn-secondary",
        "info": "btn-info",
        "warning": "btn-warning",
        "danger": "btn-danger",
        "success": "btn-success"
    }
}

# Application version
APP_VERSION = "2.0.1"

# Logging Configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'detailed': {
            'format': '{levelname} {asctime} {module} {funcName} {lineno} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
        'json': {
            'format': '%(levelname)s %(asctime)s %(module)s %(message)s',
        },
    },
    'filters': {
        'require_debug_false': {
            '()': 'django.utils.log.RequireDebugFalse',
        },
        'require_debug_true': {
            '()': 'django.utils.log.RequireDebugTrue',
        },
    },
    'handlers': {
        'console': {
            'level': 'WARNING',  # Only warnings and errors in console
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
        'console_debug': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'detailed',
            'filters': ['require_debug_true'],
        },
        'file_debug': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / 'logs' / 'debug.log',
            'maxBytes': 50 * 1024 * 1024,  # 50MB
            'backupCount': 5,
            'formatter': 'detailed',
        },
        'file_error': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / 'logs' / 'error.log',
            'maxBytes': 50 * 1024 * 1024,  # 50MB
            'backupCount': 5,
            'formatter': 'detailed',
        },
        'file_requests': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / 'logs' / 'requests.log',
            'maxBytes': 50 * 1024 * 1024,  # 50MB
            'backupCount': 5,
            'formatter': 'json',
        },
        'mail_admins': {
            'level': 'ERROR',
            'class': 'django.utils.log.AdminEmailHandler',
            'filters': ['require_debug_false'],
            'formatter': 'verbose',
        },
    },
    'loggers': {
        # Django core loggers
        'django': {
            'handlers': ['file_debug'],
            'level': 'WARNING',  # Reduce Django noise
            'propagate': False,
        },
        'django.request': {
            'handlers': ['file_error', 'mail_admins', 'console'],
            'level': 'ERROR',
            'propagate': False,
        },
        'django.security': {
            'handlers': ['file_error', 'mail_admins', 'console'],
            'level': 'ERROR',
            'propagate': False,
        },
        'django.server': {
            'handlers': ['file_requests'],  # Move server logs to requests file
            'level': 'INFO',
            'propagate': False,
        },
        'django.db.backends': {
            'handlers': ['file_debug'],
            'level': 'WARNING',  # Reduce DB query noise
            'propagate': False,
        },
        # Application loggers
        'appointments': {
            'handlers': ['file_debug'],
            'level': 'WARNING',  # Only warnings and errors
            'propagate': True,
        },
        'employees': {
            'handlers': ['file_debug'],
            'level': 'WARNING',  # Reduce employee admin noise
            'propagate': True,
        },
        'employees.admin': {
            'handlers': ['file_debug'],
            'level': 'ERROR',  # Only errors from employee admin
            'propagate': False,
        },
        'api': {
            'handlers': ['console', 'file_debug', 'file_error'],
            'level': 'WARNING',  # Only warnings and errors
            'propagate': True,
        },
        'api.views.files': {
            'handlers': ['console', 'file_debug', 'file_error'],
            'level': 'INFO',  # Keep file operations visible
            'propagate': False,
        },
        'api.middleware': {
            'handlers': ['console', 'file_debug', 'file_error'],
            'level': 'WARNING',  # Only warnings and errors
            'propagate': False,
        },
        'aws_services': {
            'handlers': ['console', 'file_debug', 'file_error'],
            'level': 'WARNING',  # Only warnings and errors for AWS
            'propagate': False,
        },
        'requests_logger': {
            'handlers': ['file_requests'],
            'level': 'INFO',
            'propagate': False,
        },
        # Root logger
        '': {
            'handlers': ['console', 'file_debug', 'file_error'],
            'level': 'WARNING',  # Only warnings and errors by default
        },
    },
}

# Google OAuth Configuration for Development
# In production, this will be overridden by AWS Secrets Manager
GOOGLE_OAUTH_CONFIG = {
    'client_id': '817722074806-q37lcl2s6am4bvua7kijb9erqglg42a7.apps.googleusercontent.com',
    'client_secret': '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'
}

# Try to import local settings if they exist
try:
    from .settings_local import *
    print("Local settings imported successfully.")
except ImportError:
    print("No local settings found. Using default settings.")
