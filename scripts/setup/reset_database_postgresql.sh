#!/bin/bash
# PostgreSQL version of database reset

set -e

print_header() {
    echo ""
    echo "======================================================"
    echo "  $1"
    echo "======================================================"
}

# Activate virtual environment
print_header "ACTIVATING VIRTUAL ENVIRONMENT"
if [ -d "venv" ]; then
    source venv/bin/activate
else
    echo "❌ Virtual environment not found"
    exit 1
fi

# Reset PostgreSQL database
print_header "RESETTING POSTGRESQL DATABASE"
echo "Dropping and recreating all tables..."

# Run migrations
python manage.py migrate --settings=settings_dev

# Run data scripts
print_header "RUNNING DATA SCRIPTS"
DATA_SCRIPTS=(
    "create_clement_lash_initial_admin.py"
    "create_clement_lash_business.py"
    "create_clement_lash_employee_profile.py"
    "create_lash_services.py"
    "create_stylist_level_services.py"
    "create_stylist_level_addons.py"
    "create_addon_suggestion_rules.py"
    "create_lash_service_suggestion_rules.py"
    "create_campaign_types_unified.py"
    "create_sample_transactions.py"
    "create_customer.py"
)

for SCRIPT in "${DATA_SCRIPTS[@]}"; do
    echo "Running $SCRIPT..."
    DJANGO_SETTINGS_MODULE=settings_dev python "scripts/data/$SCRIPT"
    echo "✅ $SCRIPT completed."
    sleep 1
done

print_header "POSTGRESQL SETUP COMPLETE"
echo "✅ PostgreSQL database has been successfully set up with initial data."