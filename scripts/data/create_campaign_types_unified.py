#!/usr/bin/env python
"""
Create Campaign Types - Unified Script

This script creates default campaign types in the database using Django ORM.
It works with both SQLite and PostgreSQL databases.

Usage:
    python scripts/data/create_campaign_types_unified.py
    
Or from Django shell:
    python manage.py shell < scripts/data/create_campaign_types_unified.py
"""

import os
import sys
import django
from pathlib import Path

# Setup Django environment
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

# Handle different settings configurations
settings_variations = [
    'config.settings',
    'settings',
    'local_settings',
    'settings_dev'
]

django_setup_success = False
for settings_module in settings_variations:
    try:
        os.environ['DJANGO_SETTINGS_MODULE'] = settings_module
        django.setup()
        django_setup_success = True
        print(f"✅ Django setup successful with {settings_module}")
        break
    except (ImportError, django.core.exceptions.ImproperlyConfigured) as e:
        print(f"⚠️  Failed to setup Django with {settings_module}: {e}")
        continue

if not django_setup_success:
    print("❌ Failed to setup Django with any settings module")
    sys.exit(1)

from django.db import connection, transaction
from marketing.models import CampaignType


def get_db_engine():
    """Get the database engine type"""
    return connection.vendor


def reset_campaign_types():
    """Reset campaign types with database-specific optimizations"""
    
    print("🔍 Detecting database type...")
    db_engine = get_db_engine()
    print(f"📊 Database engine: {db_engine}")
    
    # Define campaign types data
    campaign_types_data = [
        {
            'name': 'Email Blast',
            'description': 'Email Blast, by default',
            'supports_offset': False,
            'offset_unit': None,
            'min_offset': None,
            'max_offset': None,
            'is_system': True
        },
        {
            'name': 'Text Blast',
            'description': 'Text Blast',
            'supports_offset': False,
            'offset_unit': None,
            'min_offset': None,
            'max_offset': None,
            'is_system': True
        },
        {
            'name': 'Campaign Blast',
            'description': 'Email & Text Blast',
            'supports_offset': False,
            'offset_unit': None,
            'min_offset': None,
            'max_offset': None,
            'is_system': True
        },
        {
            'name': 'Birthday',
            'description': 'Birthday Campaign',
            'supports_offset': True,
            'offset_unit': 'days',
            'min_offset': 0,
            'max_offset': 30,
            'is_system': True
        },
        {
            'name': 'Lost Customer',
            'description': 'Lost Customer Callback',
            'supports_offset': True,
            'offset_unit': 'weeks',
            'min_offset': 1,
            'max_offset': 24,
            'is_system': True
        },
        {
            'name': 'Before Visit',
            'description': 'Before Visit',
            'supports_offset': True,
            'offset_unit': 'days',
            'min_offset': 1,
            'max_offset': 30,
            'is_system': True
        },
        {
            'name': 'After Visit',
            'description': 'After Visit',
            'supports_offset': True,
            'offset_unit': 'days',
            'min_offset': 0,
            'max_offset': 30,
            'is_system': True
        }
    ]
    
    try:
        with transaction.atomic():
            print("🗑️  Checking existing campaign types...")
            existing_count = CampaignType.objects.count()
            
            if existing_count > 0:
                print(f"📋 Found {existing_count} existing campaign types:")
                for ct in CampaignType.objects.all():
                    print(f"   - ID: {ct.id}, Name: {ct.name}")
                    
                print("🗑️  Deleting existing campaign types...")
                CampaignType.objects.all().delete()
                print("✅ Existing campaign types deleted")
            else:
                print("📝 No existing campaign types found")
            
            # Reset sequence if PostgreSQL
            if db_engine == 'postgresql':
                print("🔄 Resetting PostgreSQL sequence...")
                with connection.cursor() as cursor:
                    cursor.execute("SELECT setval('marketing_campaigntype_id_seq', 1, false);")
                print("✅ PostgreSQL sequence reset")
            elif db_engine == 'sqlite':
                print("🔄 Resetting SQLite sequence...")
                with connection.cursor() as cursor:
                    cursor.execute("DELETE FROM sqlite_sequence WHERE name='marketing_campaigntype';")
                print("✅ SQLite sequence reset")
            
            # Create new campaign types
            print("📝 Creating new campaign types...")
            created_types = []
            
            for ct_data in campaign_types_data:
                ct = CampaignType.objects.create(**ct_data)
                created_types.append(ct)
                print(f"   ✅ Created: ID: {ct.id}, Name: {ct.name}")
            
            print(f"\n🎉 Successfully created {len(created_types)} campaign types!")
            
            # Verify results
            print("\n🔍 Verification:")
            final_count = CampaignType.objects.count()
            print(f"📊 Total campaign types in database: {final_count}")
            
            if final_count != len(campaign_types_data):
                raise Exception(f"Expected {len(campaign_types_data)} types, but found {final_count}")
            
            print("✅ All campaign types created successfully!")
            return True
            
    except Exception as e:
        print(f"❌ Error creating campaign types: {e}")
        return False


def main():
    """Main execution function"""
    print("🚀 Starting Campaign Types Creation...")
    print("=" * 60)
    
    success = reset_campaign_types()
    
    print("=" * 60)
    if success:
        print("🎉 Campaign types setup completed successfully!")
        return 0
    else:
        print("❌ Campaign types setup failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
