-- Reset Campaign Types SQL

-- 备份现有数据（如果需要）
-- CREATE TEMPORARY TABLE temp_campaign_types AS SELECT * FROM customers_campaigntype;

-- 找出并暂时禁用外键约束
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (
        SELECT
            tc.constraint_name,
            tc.table_name,
            tc.constraint_schema
        FROM
            information_schema.table_constraints tc
            JOIN information_schema.constraint_column_usage ccu ON tc.constraint_name = ccu.constraint_name
        WHERE
            tc.constraint_type = 'FOREIGN KEY'
            AND ccu.table_name = 'customers_campaigntype'
    ) LOOP
        EXECUTE format('ALTER TABLE %I.%I DROP CONSTRAINT %I', r.constraint_schema, r.table_name, r.constraint_name);
        RAISE NOTICE 'Dropped foreign key constraint: %', r.constraint_name;
    END LOOP;
END $$;

-- 删除现有的campaign types
DELETE FROM customers_campaigntype;

-- 强制重置ID序列
ALTER SEQUENCE customers_campaigntype_id_seq RESTART WITH 1;

-- 插入新的campaign types
INSERT INTO customers_campaigntype (name, description, supports_offset, offset_unit, min_offset, max_offset, is_system, created_at, updated_at) 
VALUES ('Email Blast', 'Email Blast, by default', FALSE, NULL, NULL, NULL, TRUE, NOW(), NOW());

INSERT INTO customers_campaigntype (name, description, supports_offset, offset_unit, min_offset, max_offset, is_system, created_at, updated_at) 
VALUES ('Text Blast', 'Text Blast', FALSE, NULL, NULL, NULL, TRUE, NOW(), NOW());

INSERT INTO customers_campaigntype (name, description, supports_offset, offset_unit, min_offset, max_offset, is_system, created_at, updated_at) 
VALUES ('Campaign Blast', 'Email & Text Blast', FALSE, NULL, NULL, NULL, TRUE, NOW(), NOW());

INSERT INTO customers_campaigntype (name, description, supports_offset, offset_unit, min_offset, max_offset, is_system, created_at, updated_at) 
VALUES ('Birthday', 'Birthday Campaign', TRUE, 'days', 0, 30, TRUE, NOW(), NOW());

INSERT INTO customers_campaigntype (name, description, supports_offset, offset_unit, min_offset, max_offset, is_system, created_at, updated_at) 
VALUES ('Lost Customer', 'Lost Customer Callback', TRUE, 'weeks', 1, 24, TRUE, NOW(), NOW());

INSERT INTO customers_campaigntype (name, description, supports_offset, offset_unit, min_offset, max_offset, is_system, created_at, updated_at) 
VALUES ('Before Visit', 'Before Visit', TRUE, 'days', 1, 30, TRUE, NOW(), NOW());

INSERT INTO customers_campaigntype (name, description, supports_offset, offset_unit, min_offset, max_offset, is_system, created_at, updated_at) 
VALUES ('After Visit', 'After Visit', TRUE, 'days', 0, 30, TRUE, NOW(), NOW());

-- 验证结果
SELECT id, name FROM customers_campaigntype ORDER BY id;

-- 更新引用campaign_type的表
-- 这里需要根据实际情况添加更新语句
-- 例如: UPDATE customers_campaign SET campaign_type_id = 1 WHERE campaign_type_id = 4;

-- 注意：如果需要恢复外键约束，需要在此处添加相应的ALTER TABLE语句
-- 例如: ALTER TABLE customers_campaign ADD CONSTRAINT fk_campaign_type FOREIGN KEY (campaign_type_id) REFERENCES customers_campaigntype(id); 