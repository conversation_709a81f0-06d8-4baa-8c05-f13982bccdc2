# Calendar Import Script Documentation

## Overview

The `create_import_appointments_to_calendar.py` script is designed to import ICS calendar files into the Chatbook appointment system. It features advanced regex parsing and fuzzy matching to automatically map calendar entries to existing services and add-ons.

## Quick Start

### Basic Commands

```bash
# 1. Inspect ICS file (read-only analysis)
python scripts/data/create_import_appointments_to_calendar.py inspect --ics-file scripts/sample-import-data/DateRange_1July2025-31July2025.ics

# 2. Dry run validation (test without importing)
python scripts/data/create_import_appointments_to_calendar.py dry-run --business-id 1 --employee-id 2 --ics-file scripts/sample-import-data/DateRange_1July2025-31July2025.ics

# 3. Actual import (creates appointments)
python scripts/data/create_import_appointments_to_calendar.py import --business-id 1 --employee-id 2 --ics-file scripts/sample-import-data/DateRange_1July2025-31July2025.ics
```

## Command Reference

### Available Actions

| Action | Description | Required Parameters |
|--------|-------------|-------------------|
| `inspect` | Analyze ICS file structure and content | `--ics-file` |
| `dry-run` | Validate appointments without saving | `--business-id`, `--employee-id`, `--ics-file` |
| `import` | Actually create appointments in database | `--business-id`, `--employee-id`, `--ics-file` |
| `list-businesses` | Show available business and employee IDs | None |
| `delete-imported` | Remove previously imported appointments | `--business-id`, `--employee-id` |

### Parameters

| Parameter | Required | Description | Example |
|-----------|----------|-------------|---------|
| `--ics-file` | Yes (except list-businesses) | Path to ICS calendar file | `scripts/sample-import-data/DateRange_1July2025-31July2025.ics` |
| `--business-id` | Yes (for most actions) | Target business ID | `1` |
| `--employee-id` | Yes (for most actions) | Employee to assign appointments to | `2` |
| `--create-customers` | Optional | Create missing customers (default: true) | `--create-customers` |
| `--create-services` | Optional | Create missing services (default: false) | `--create-services` |

## Step-by-Step Workflow

### Step 1: Find Business and Employee IDs

```bash
python scripts/data/create_import_appointments_to_calendar.py list-businesses
```

**Output:**
```
Available Businesses:
   ID: 1 | Name: Clement Lash
   Employees:
     ID: 2 | Carol Fan
```

### Step 2: Inspect Your ICS File

```bash
python scripts/data/create_import_appointments_to_calendar.py inspect --ics-file path/to/your/calendar.ics
```

**What it shows:**
- Total events and date range
- Field analysis (SUMMARY, DTSTART, DTEND, etc.)
- Sample events with parsed timestamps
- Unique appointment types
- Service category breakdown

### Step 3: Test with Dry Run

```bash
python scripts/data/create_import_appointments_to_calendar.py dry-run --business-id 1 --employee-id 2 --ics-file path/to/your/calendar.ics
```

**What it validates:**
- Service fuzzy matching with confidence scores
- Customer matching/creation requirements
- Add-on detection and matching
- Shows warnings for missing data
- Reports errors that would prevent import

### Step 4: Run Actual Import

```bash
python scripts/data/create_import_appointments_to_calendar.py import --business-id 1 --employee-id 2 --ics-file path/to/your/calendar.ics
```

**What it creates:**
- Appointments with accurate ICS durations
- New customers (with @imported.local emails)
- AppointmentAddOn records for detected add-ons
- Links to existing services via fuzzy matching

## Fuzzy Matching System

The script uses advanced fuzzy matching to automatically map calendar entries to your existing services:

### Service Matching Examples

| ICS Calendar Entry | Matched Service | Confidence |
|-------------------|-----------------|------------|
| `Eileen Conover - Classic Within 2-week refill` | `Classic Within 2-week refill` (ID: 5) | 100% |
| `Chloe Ortiz - Styling Lash Fullset(S01-S08)` | `Styling Lash Fullset(S01-S08)` (ID: 2) | 82% |
| `Vi Nguyen - Volume Within 2-week refill with Bottom Lash(as an add-on service)` | `Volume Within 2-week refill` (ID: 7) + `Bottom Lash` (Add-on ID: 1) | 100% |

### Confidence Levels

- **90-100%**: Perfect or near-perfect matches
- **70-89%**: High confidence matches (service codes cause slight reduction)
- **50-69%**: Acceptable matches with manual review recommended
- **<50%**: Failed matches - service will need to be created or matched manually

## Expected Results

### Carol Fan Import Example (July 2025)

Based on the sample data:
- **90 appointments** created successfully
- **71 new customers** with auto-generated contact info
- **99% fuzzy matching** success rate
- **Automatic add-ons** (Bottom Lash, Consulting Sessions, Lash Removal)
- **Accurate durations** from ICS file instead of service defaults

### Success Output

```
IMPORT RESULTS:
   Total events processed: 122
   Client appointments: 91
   Customers created: 71
   Customers matched: 20
   Services created: 0
   Services matched: 90
   Appointments created: 90
   Appointments skipped: 1

SUCCESS: Import completed
```

## Advanced Features

### Customer Creation

**Automatic customer creation includes:**
- Email: `<EMAIL>`
- Phone: Auto-generated `+1555XXXXXXX`
- Names: Parsed from appointment summaries
- Business relationship: Linked to target business

### Add-on Detection

**Automatically detects and creates:**
- `with Bottom Lash(as an add-on service)` → Bottom Lash add-on
- `with Consulting Session` → Consulting Session add-on
- `with Lash Removal` → Lash Removal add-on
- `with 2 add-ons`, `with 3 add-ons` → Multiple add-on notation

### Duration Calculation

**Uses actual ICS times:**
- Parses `DTSTART` and `DTEND` from calendar
- Calculates exact duration in minutes
- Overrides default service durations
- Example: 1:20 appointment duration vs 1:45 service default

## Troubleshooting

### Common Issues

#### 1. Service Matching Failures
```
Service not found: Other Services Client Care (fuzzy match failed)
```
**Solution:** Either create the missing service manually or skip these appointments

#### 2. Customer Duplicates
```
Will create new customer: John Smith
```
**Solution:** Review customer list after import and merge duplicates if needed

#### 3. Database Connection Issues
```
Failed to retrieve database password
Using local SQLite database for development
```
**Solution:** This is normal - script automatically falls back to local database

### Database Cleanup

**Remove imported appointments:**
```bash
python scripts/data/create_import_appointments_to_calendar.py delete-imported --business-id 1 --employee-id 2
```

**What it removes:**
- All appointments for the employee in July 2025 date range
- Associated AppointmentService records
- Associated AppointmentAddOn records
- Optionally: customers with @imported.local emails

## File Format Requirements

### Supported ICS Format

The script expects standard ICS format with these fields:
```
BEGIN:VEVENT
DTSTART:20250709T170000Z
DTEND:20250709T180000Z
UID:unique-identifier
DTSTAMP:20250729T185222Z
SUMMARY:Customer Name - Service Description
DESCRIPTION:
END:VEVENT
```

### Appointment Summary Patterns

**Client appointments (automatically detected):**
- `Customer Name - Service Type`
- Example: `Eileen Conover - Classic Within 2-week refill`

**Personal events (automatically skipped):**
- `Workout`, `Badminton`, `Lunch`, `Dental`, etc.

**Staff transitions (automatically skipped):**
- `Cait>Carol`, `Carol>Serena(Master Test)`

## Success Tips

1. **Start with inspect mode** to understand your data
2. **Always run dry-run first** to validate matching
3. **Review confidence scores** - anything below 70% needs attention
4. **Check add-on detection** in dry-run output
5. **Use delete-imported** if you need to re-run import

## Support

For issues with the import script:
1. Check the console output for specific error messages
2. Verify your ICS file format matches the examples
3. Ensure business and employee IDs are correct
4. Review fuzzy matching confidence scores for failed matches

The script provides detailed logging throughout the process to help diagnose any issues.