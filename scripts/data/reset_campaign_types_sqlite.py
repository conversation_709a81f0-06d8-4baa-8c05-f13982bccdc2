#!/usr/bin/env python
"""
重置Campaign Types (SQLite版)

这个脚本重置SQLite数据库中的campaign types：
1. 删除所有现有的campaign types
2. 重置ID序列从1开始
3. 创建新的campaign types，ID从1开始

使用方法:
    python scripts/data/reset_campaign_types_sqlite.py
"""

import os
import sys
import sqlite3
import datetime

# 获取项目根目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
DB_PATH = os.path.join(PROJECT_ROOT, 'db.sqlite3')

def now():
    """返回当前时间的ISO格式字符串"""
    return datetime.datetime.now().isoformat()

def main():
    """执行SQLite命令重置campaign types"""
    try:
        # 检查数据库文件是否存在
        if not os.path.exists(DB_PATH):
            print(f"错误: 数据库文件不存在: {DB_PATH}")
            return 1
            
        print(f"使用数据库: {DB_PATH}")
        
        # 连接数据库
        print("正在连接数据库...")
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        try:
            # 开始事务
            conn.execute("BEGIN TRANSACTION;")
            
            # 备份现有数据
            print("备份现有campaign types...")
            cursor.execute("SELECT * FROM customers_campaigntype;")
            existing_types = cursor.fetchall()
            for ct in existing_types:
                print(f"ID: {ct['id']}, Name: {ct['name']}")
            
            # 检查是否有引用campaign types的表
            print("\n检查引用关系...")
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND sql LIKE '%REFERENCES customers_campaigntype%';
            """)
            referencing_tables = cursor.fetchall()
            
            if referencing_tables:
                print("以下表引用了customers_campaigntype:")
                for table in referencing_tables:
                    print(f"- {table['name']}")
                    
                # 提示用户继续
                response = input("\n继续操作将破坏这些引用关系。是否继续? (y/n): ")
                if response.lower() != 'y':
                    print("操作已取消")
                    return 0
            
            # 删除现有的campaign types
            print("\n删除现有campaign types...")
            cursor.execute("DELETE FROM customers_campaigntype;")
            
            # 重置SQLite序列
            print("重置ID序列...")
            cursor.execute("DELETE FROM sqlite_sequence WHERE name='customers_campaigntype';")
            
            # 定义新的campaign types
            campaign_types = [
                ('Email Blast', 'Email Blast, by default', 0, None, None, None, 1, now(), now()),
                ('Text Blast', 'Text Blast', 0, None, None, None, 1, now(), now()),
                ('Campaign Blast', 'Email & Text Blast', 0, None, None, None, 1, now(), now()),
                ('Birthday', 'Birthday Campaign', 1, 'days', 0, 30, 1, now(), now()),
                ('Lost Customer', 'Lost Customer Callback', 1, 'weeks', 1, 24, 1, now(), now()),
                ('Before Visit', 'Before Visit', 1, 'days', 1, 30, 1, now(), now()),
                ('After Visit', 'After Visit', 1, 'days', 0, 30, 1, now(), now())
            ]
            
            # 插入新的campaign types
            print("插入新的campaign types...")
            for ct_data in campaign_types:
                cursor.execute("""
                    INSERT INTO customers_campaigntype 
                    (name, description, supports_offset, offset_unit, min_offset, max_offset, is_system, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?);
                """, ct_data)
            
            # 验证结果
            print("\n验证结果:")
            cursor.execute("SELECT id, name FROM customers_campaigntype ORDER BY id;")
            results = cursor.fetchall()
            for row in results:
                print(f"ID: {row['id']}, Name: {row['name']}")
            
            # 提交事务
            print("\n提交事务...")
            conn.commit()
            print("Campaign types重置成功!")
            
        except Exception as e:
            # 回滚事务
            conn.rollback()
            print(f"错误: {e}")
            print("操作已回滚")
            return 1
        finally:
            # 关闭游标和连接
            cursor.close()
            conn.close()
            
    except Exception as e:
        print(f"连接数据库时出错: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 