#!/usr/bin/env python
"""
Reset Campaign Types

This script resets the campaign types table by:
1. Deleting all existing campaign types
2. Resetting the ID sequence to start from 1
3. Creating new campaign types with sequential IDs starting from 1

Usage:
    python manage.py shell < scripts/data/reset_campaign_types.py
"""

import os
import sys
import django

# Setup Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
try:
    django.setup()
except ImportError:
    # Try alternative settings path
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.__init__')
    django.setup()

from django.db import connection
from customers.models import CampaignType, Campaign

# Check if there are any campaigns using these types
campaigns_count = Campaign.objects.count()
if campaigns_count > 0:
    print(f"WARNING: There are {campaigns_count} campaigns in the database.")
    confirmation = input("This script will break references to campaign types. Continue? (y/n): ")
    if confirmation.lower() != 'y':
        print("Operation cancelled.")
        sys.exit(1)

# Backup existing campaign types
existing_types = list(CampaignType.objects.all().values())
print(f"Backing up {len(existing_types)} existing campaign types...")
for ct in existing_types:
    print(f"ID: {ct['id']}, Name: {ct['name']}")

# Delete all campaign types
print("Deleting all campaign types...")
CampaignType.objects.all().delete()

# Reset the ID sequence
with connection.cursor() as cursor:
    cursor.execute("ALTER SEQUENCE customers_campaigntype_id_seq RESTART WITH 1")
    print("Reset ID sequence to start from 1")

# Create new campaign types
print("Creating new campaign types...")

# Define the campaign types to create
campaign_types = [
    {
        'name': 'Email Blast',
        'description': 'Email Blast, by default',
        'supports_offset': False,
        'offset_unit': None,
        'min_offset': None,
        'max_offset': None,
        'is_system': True
    },
    {
        'name': 'Text Blast',
        'description': 'Text Blast',
        'supports_offset': False,
        'offset_unit': None,
        'min_offset': None,
        'max_offset': None,
        'is_system': True
    },
    {
        'name': 'Campaign Blast',
        'description': 'Email & Text Blast',
        'supports_offset': False,
        'offset_unit': None,
        'min_offset': None,
        'max_offset': None,
        'is_system': True
    },
    {
        'name': 'Birthday',
        'description': 'Birthday Campaign',
        'supports_offset': True,
        'offset_unit': 'days',
        'min_offset': 0,
        'max_offset': 30,
        'is_system': True
    },
    {
        'name': 'Lost Customer',
        'description': 'Lost Customer Callback',
        'supports_offset': True,
        'offset_unit': 'weeks',
        'min_offset': 1,
        'max_offset': 24,
        'is_system': True
    },
    {
        'name': 'Before Visit',
        'description': 'Before Visit',
        'supports_offset': True,
        'offset_unit': 'days',
        'min_offset': 1,
        'max_offset': 30,
        'is_system': True
    },
    {
        'name': 'After Visit',
        'description': 'After Visit',
        'supports_offset': True,
        'offset_unit': 'days',
        'min_offset': 0,
        'max_offset': 30,
        'is_system': True
    }
]

# Create the campaign types
for ct_data in campaign_types:
    ct = CampaignType.objects.create(**ct_data)
    print(f"Created: ID: {ct.id}, Name: {ct.name}")

print("\nCampaign types reset complete!")
print("New campaign type IDs:")
for ct in CampaignType.objects.all():
    print(f"ID: {ct.id}, Name: {ct.name}")

# Create SQL file with the reset commands
sql_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'reset_campaign_types.sql')
with open(sql_path, 'w') as f:
    f.write("-- Reset Campaign Types SQL\n\n")
    f.write("-- Delete existing campaign types\n")
    f.write("DELETE FROM customers_campaigntype;\n\n")
    
    f.write("-- Reset ID sequence\n")
    f.write("ALTER SEQUENCE customers_campaigntype_id_seq RESTART WITH 1;\n\n")
    
    f.write("-- Insert new campaign types\n")
    for ct_data in campaign_types:
        values = [
            f"'{ct_data['name']}'",
            f"'{ct_data['description']}'",
            'TRUE' if ct_data['supports_offset'] else 'FALSE',
            f"'{ct_data['offset_unit']}'" if ct_data['offset_unit'] else 'NULL',
            str(ct_data['min_offset']) if ct_data['min_offset'] is not None else 'NULL',
            str(ct_data['max_offset']) if ct_data['max_offset'] is not None else 'NULL',
            'TRUE' if ct_data['is_system'] else 'FALSE',
            'NOW()',  # created_at
            'NOW()'   # updated_at
        ]
        sql = f"INSERT INTO customers_campaigntype (name, description, supports_offset, offset_unit, min_offset, max_offset, is_system, created_at, updated_at) VALUES ({', '.join(values)});\n"
        f.write(sql)

print(f"\nSQL file created at: {sql_path}") 