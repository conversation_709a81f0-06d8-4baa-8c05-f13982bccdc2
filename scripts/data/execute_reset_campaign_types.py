#!/usr/bin/env python
"""
执行重置Campaign Types的SQL脚本

这个脚本直接执行SQL命令来重置campaign types，不依赖Django ORM。
它会：
1. 删除所有现有的campaign types
2. 重置ID序列从1开始
3. 创建新的campaign types，ID从1开始

使用方法:
    python scripts/data/execute_reset_campaign_types.py
"""

import os
import sys
import psycopg2
from psycopg2.extras import DictCursor

# 数据库连接参数 - 根据实际环境修改
DB_PARAMS = {
    'dbname': 'chatbook',  # 修改为您的数据库名
    'user': 'postgres',    # 修改为您的数据库用户名
    'password': '',        # 修改为您的数据库密码
    'host': 'localhost',   # 修改为您的数据库主机
    'port': '5432'         # 修改为您的数据库端口
}

# SQL命令
SQL_COMMANDS = [
    """
    -- 查找并暂时禁用外键约束
    DO $$
    DECLARE
        r RECORD;
    BEGIN
        FOR r IN (
            SELECT
                tc.constraint_name,
                tc.table_name,
                tc.constraint_schema
            FROM
                information_schema.table_constraints tc
                JOIN information_schema.constraint_column_usage ccu ON tc.constraint_name = ccu.constraint_name
            WHERE
                tc.constraint_type = 'FOREIGN KEY'
                AND ccu.table_name = 'customers_campaigntype'
        ) LOOP
            EXECUTE format('ALTER TABLE %I.%I DROP CONSTRAINT %I', r.constraint_schema, r.table_name, r.constraint_name);
            RAISE NOTICE 'Dropped foreign key constraint: %', r.constraint_name;
        END LOOP;
    END $$;
    """,
    
    # 删除现有的campaign types
    "DELETE FROM customers_campaigntype;",
    
    # 重置ID序列
    "ALTER SEQUENCE customers_campaigntype_id_seq RESTART WITH 1;",
    
    # 插入新的campaign types
    """
    INSERT INTO customers_campaigntype (name, description, supports_offset, offset_unit, min_offset, max_offset, is_system, created_at, updated_at) 
    VALUES ('Email Blast', 'Email Blast, by default', FALSE, NULL, NULL, NULL, TRUE, NOW(), NOW());
    """,
    
    """
    INSERT INTO customers_campaigntype (name, description, supports_offset, offset_unit, min_offset, max_offset, is_system, created_at, updated_at) 
    VALUES ('Text Blast', 'Text Blast', FALSE, NULL, NULL, NULL, TRUE, NOW(), NOW());
    """,
    
    """
    INSERT INTO customers_campaigntype (name, description, supports_offset, offset_unit, min_offset, max_offset, is_system, created_at, updated_at) 
    VALUES ('Campaign Blast', 'Email & Text Blast', FALSE, NULL, NULL, NULL, TRUE, NOW(), NOW());
    """,
    
    """
    INSERT INTO customers_campaigntype (name, description, supports_offset, offset_unit, min_offset, max_offset, is_system, created_at, updated_at) 
    VALUES ('Birthday', 'Birthday Campaign', TRUE, 'days', 0, 30, TRUE, NOW(), NOW());
    """,
    
    """
    INSERT INTO customers_campaigntype (name, description, supports_offset, offset_unit, min_offset, max_offset, is_system, created_at, updated_at) 
    VALUES ('Lost Customer', 'Lost Customer Callback', TRUE, 'weeks', 1, 24, TRUE, NOW(), NOW());
    """,
    
    """
    INSERT INTO customers_campaigntype (name, description, supports_offset, offset_unit, min_offset, max_offset, is_system, created_at, updated_at) 
    VALUES ('Before Visit', 'Before Visit', TRUE, 'days', 1, 30, TRUE, NOW(), NOW());
    """,
    
    """
    INSERT INTO customers_campaigntype (name, description, supports_offset, offset_unit, min_offset, max_offset, is_system, created_at, updated_at) 
    VALUES ('After Visit', 'After Visit', TRUE, 'days', 0, 30, TRUE, NOW(), NOW());
    """
]

def main():
    """执行SQL命令重置campaign types"""
    try:
        # 连接数据库
        print("正在连接数据库...")
        conn = psycopg2.connect(**DB_PARAMS)
        conn.autocommit = False  # 使用事务
        cursor = conn.cursor(cursor_factory=DictCursor)
        
        try:
            # 执行SQL命令
            print("正在执行SQL命令...")
            for i, sql in enumerate(SQL_COMMANDS, 1):
                print(f"执行命令 {i}/{len(SQL_COMMANDS)}...")
                cursor.execute(sql)
            
            # 查询结果
            print("\n验证结果:")
            cursor.execute("SELECT id, name FROM customers_campaigntype ORDER BY id;")
            results = cursor.fetchall()
            for row in results:
                print(f"ID: {row['id']}, Name: {row['name']}")
            
            # 提交事务
            print("\n提交事务...")
            conn.commit()
            print("Campaign types重置成功!")
            
        except Exception as e:
            # 回滚事务
            conn.rollback()
            print(f"错误: {e}")
            print("操作已回滚")
            return 1
        finally:
            # 关闭游标和连接
            cursor.close()
            conn.close()
            
    except Exception as e:
        print(f"连接数据库时出错: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 