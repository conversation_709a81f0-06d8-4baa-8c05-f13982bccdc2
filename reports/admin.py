from django.contrib import admin
from .models import Transaction


@admin.register(Transaction)
class TransactionAdmin(admin.ModelAdmin):
    list_display = [
        'id',
        'customer_name',
        'service_name', 
        'sold_by',
        'checkout_by',
        'checkout_date',
        'payment_method',
        'amount_paid',
    ]
    
    list_filter = [
        'checkout_date',
        'payment_method',
        'sold_by',
        'checkout_by',
        'service',
    ]
    
    search_fields = [
        'customer__customer__user__first_name',
        'customer__customer__user__last_name',
        'customer__customer__user__email',
        'service__name',
        'item_sold',
    ]
    
    readonly_fields = [
        'id',
        'customer_name',
        'service_name',
        'subtotal',
        'total_before_tip',
        'created_at',
        'updated_at',
    ]
    
    fieldsets = (
        ('Transaction Info', {
            'fields': ('id', 'checkout_date', 'customer', 'service')
        }),
        ('Staff', {
            'fields': ('sold_by', 'checkout_by')
        }),
        ('Items & Service', {
            'fields': ('item_sold', 'quantity', 'service_name')
        }),
        ('Financial Details', {
            'fields': (
                'price', 'tax', 'tip', 'discount', 
                'subtotal', 'total_before_tip', 'amount_paid',
                'payment_method'
            )
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    date_hierarchy = 'checkout_date'
    ordering = ['-checkout_date']
    
    def customer_name(self, obj):
        return obj.customer_name
    customer_name.short_description = 'Customer'
    
    def service_name(self, obj):
        return obj.service_name
    service_name.short_description = 'Service' 