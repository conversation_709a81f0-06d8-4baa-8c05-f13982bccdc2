"""
Development settings for chatbook-backend that help bypass migration checks.
Import this after the main settings to override specific settings.

Usage:
    python manage.py runserver --settings=settings_dev
"""

from settings import *  # Import all settings from the main settings file

# Ignore migrations to avoid circular dependencies
MIGRATION_MODULES = {
    'bookings': None,  # Tell Django to ignore bookings migrations
    # 'services': None,  # Ignore services migrations too
    # 'employees': None,  # Ignore employee migrations
    # 'appointments': None,  # Ignore appointment migrations
}

# Additional development-specific settings
DEBUG = True
ALLOWED_HOSTS = ['localhost', '127.0.0.1', '*.ngrok.io', '*.ngrok-free.app', 'b61dd41593b1.ngrok-free.app'] 

# Add query performance logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'django.db.backends': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'django.request': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}

# Connection pooling for better performance
if 'DATABASES' in locals() and 'default' in DATABASES:
    DATABASES['default']['CONN_MAX_AGE'] = 60

print("🔍 Debug logging enabled - SQL queries will be logged")