from django import forms
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from .models import Employee
from django.contrib.auth import get_user_model

User = get_user_model()

class EmployeeForm(forms.ModelForm):
    DAYS_CHOICES = [
        ('Mon', 'Monday'),
        ('Tue', 'Tuesday'),
        ('Wed', 'Wednesday'),
        ('Thu', 'Thursday'),
        ('Fri', 'Friday'),
        ('Sat', 'Saturday'),
        ('Sun', 'Sunday'),
    ]
    
    # User model fields
    first_name = forms.CharField(max_length=150, required=True, widget=forms.TextInput(attrs={'class': 'form-control'}))
    last_name = forms.CharField(max_length=150, required=True, widget=forms.TextInput(attrs={'class': 'form-control'}))
    email = forms.EmailField(required=True, widget=forms.EmailInput(attrs={'class': 'form-control'}))
    
    # Social media fields with better form handling
    instagram_url = forms.URLField(required=False, widget=forms.URLInput(attrs={'class': 'form-control', 'placeholder': 'https://instagram.com/...'}))
    facebook_url = forms.URLField(required=False, widget=forms.URLInput(attrs={'class': 'form-control', 'placeholder': 'https://facebook.com/...'}))
    twitter_url = forms.URLField(required=False, widget=forms.URLInput(attrs={'class': 'form-control', 'placeholder': 'https://twitter.com/...'}))
    linkedin_url = forms.URLField(required=False, widget=forms.URLInput(attrs={'class': 'form-control', 'placeholder': 'https://linkedin.com/in/...'}))
    
    work_days = forms.MultipleChoiceField(
        choices=DAYS_CHOICES,
        widget=forms.CheckboxSelectMultiple,
        required=False,
        help_text="Select the days this employee typically works"
    )
    
    commission_rate = forms.DecimalField(
        max_digits=5, 
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Commission percentage (0-100)",
        widget=forms.NumberInput(attrs={'step': '0.01', 'min': '0', 'max': '100'})
    )
    
    profile_image = forms.ImageField(
        required=False,
        widget=forms.ClearableFileInput(attrs={'class': 'form-control'})
    )
    
    # Email confirmation field
    confirm_email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Confirm email address'
        })
    )
    
    class Meta:
        model = Employee
        fields = [
            # Personal Information
            'first_name', 'last_name', 'stylist_level', 'email',
            # 'mobile_phone',  # TODO: This field doesn't exist in Employee model
            'profile_image',
            
            # Calendar Integration
            'calendar_sync_enabled', 'calendar_provider', 'calendar_id',
            
            # Working Hours
            'work_days', 
            # 'work_hours_start', 'work_hours_end',  # TODO: These fields don't exist in Employee model
            # 'break_start', 'break_end',  # TODO: These fields don't exist in Employee model
            
            # Services & Booking Settings
            'accept_online_bookings',
            
            # Access Control
            'employee_type', 'access_level', 'is_active'
        ]
        widgets = {
            # Personal Information
            'first_name': forms.TextInput(attrs={'class': 'form-control'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control'}),
            'stylist_level': forms.Select(attrs={'class': 'form-select'}),
            'email': forms.EmailInput(attrs={'class': 'form-control'}),
            # 'mobile_phone': forms.TextInput(attrs={'class': 'form-control'}),  # TODO: Field doesn't exist
            'profile_image': forms.ClearableFileInput(attrs={'class': 'form-control'}),
            
            # Calendar Integration
            'calendar_sync_enabled': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'calendar_provider': forms.Select(attrs={'class': 'form-select'}),
            'calendar_id': forms.TextInput(attrs={'class': 'form-control'}),
            
            # Working Hours
            # 'work_hours_start': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),  # TODO: Field doesn't exist
            # 'work_hours_end': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),  # TODO: Field doesn't exist
            # 'break_start': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),  # TODO: Field doesn't exist
            # 'break_end': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),  # TODO: Field doesn't exist
            
            # Services & Booking Settings
            'accept_online_bookings': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            
            # Access Control
            'employee_type': forms.Select(attrs={'class': 'form-select'}),
            'access_level': forms.Select(attrs={'class': 'form-select'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'})
        }
    
    def __init__(self, *args, **kwargs):
        instance = kwargs.get('instance', None)
        if instance:
            # Pre-populate user fields when editing
            initial = kwargs.get('initial', {})
            initial['first_name'] = instance.user.first_name
            initial['last_name'] = instance.user.last_name
            initial['email'] = instance.user.email
            kwargs['initial'] = initial
        super().__init__(*args, **kwargs)
    
    def clean(self):
        cleaned_data = super().clean()
        email = cleaned_data.get('email')
        confirm_email = cleaned_data.get('confirm_email')
        
        if email and confirm_email and email != confirm_email:
            raise forms.ValidationError({
                'confirm_email': 'Email addresses must match.'
            })
        
        # Validate working hours
        work_hours_start = cleaned_data.get('work_hours_start')
        work_hours_end = cleaned_data.get('work_hours_end')
        break_start = cleaned_data.get('break_start')
        break_end = cleaned_data.get('break_end')
        
        if work_hours_start and work_hours_end and work_hours_start >= work_hours_end:
            raise forms.ValidationError({
                'work_hours_end': 'End time must be after start time.'
            })
        
        if break_start and break_end and break_start >= break_end:
            raise forms.ValidationError({
                'break_end': 'Break end time must be after break start time.'
            })
        
        if all([work_hours_start, work_hours_end, break_start, break_end]):
            if break_start < work_hours_start or break_end > work_hours_end:
                raise forms.ValidationError({
                    'break_start': 'Break times must be within working hours.'
                })
        
        return cleaned_data
    
    def clean_work_days(self):
        work_days = self.cleaned_data.get('work_days')
        if work_days:
            return ','.join(sorted(work_days))
        return ''
    
    def save(self, commit=True):
        employee = super().save(commit=False)
        
        # Update linked user model if provided
        if employee.user:
            user = employee.user
            user.first_name = self.cleaned_data['first_name']
            user.last_name = self.cleaned_data['last_name']
            user.email = self.cleaned_data['email']
            if commit:
                user.save()
                
        if commit:
            employee.save()
        return employee 