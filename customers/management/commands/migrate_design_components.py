from django.core.management.base import BaseCommand
from customers.models import Campaign, CampaignChannel


class Command(BaseCommand):
    help = 'Migrate design_components from Campaign to raw_components in CampaignChannel'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting migration of design components...'))
        
        # 获取所有有design_components的Campaign
        campaigns = Campaign.objects.filter(design_components__isnull=False)
        self.stdout.write(f'Found {campaigns.count()} campaigns with design components')
        
        migrated_count = 0
        skipped_count = 0
        
        for campaign in campaigns:
            # 获取该Campaign的email类型的Channel
            email_channels = campaign.channels.filter(channel_type='email')
            
            if not email_channels.exists():
                self.stdout.write(self.style.WARNING(f'Campaign {campaign.id} has no email channels, skipping'))
                skipped_count += 1
                continue
            
            # 将design_components迁移到每个email channel的raw_components
            for channel in email_channels:
                channel.raw_components = campaign.design_components
                channel.save(update_fields=['raw_components'])
                self.stdout.write(f'Migrated design components from Campaign {campaign.id} to Channel {channel.id}')
                migrated_count += 1
        
        self.stdout.write(self.style.SUCCESS(f'Migration completed. {migrated_count} channels updated, {skipped_count} campaigns skipped.')) 