from django.db import models
from django.conf import settings
from django.utils import timezone
import os
import uuid
from datetime import datetime, timedelta
from django.utils import timezone


def upload_file_path(instance, filename):
    """Generate a secure file path for uploaded files"""
    # Get file extension
    ext = filename.split('.')[-1].lower()
    
    # Get current timestamp for organization
    now = datetime.now()
    year_month = now.strftime('%Y-%m')
    
    # Create secure filename using only file_id and original name
    # Remove extension from original filename
    original_name = '.'.join(filename.split('.')[:-1])
    
    # Sanitize original filename (remove special chars, keep only alphanumeric and basic chars)
    import re
    sanitized_name = re.sub(r'[^a-zA-Z0-9_\-]', '_', original_name)
    
    # Create secure filename: {file_id}_{sanitized_original_name}.{ext}
    # No user information for security/privacy
    new_filename = f"{instance.file_id}_{sanitized_name}.{ext}"
    
    # Return path: uploads/imports/YYYY-MM/file_id_filename.ext
    return f'uploads/imports/{year_month}/{new_filename}'


class CustomerProfile(models.Model):
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='customer_profile')
    card_on_file = models.BooleanField(default=False)
    
    # General customer information
    birthdate = models.CharField(max_length=100, blank=True, null=True, help_text="Customer's birthdate")
    gender = models.CharField(max_length=50, blank=True, null=True, help_text="Customer's gender")
    
    # Address information
    address_street = models.CharField(max_length=255, blank=True, null=True, help_text="Street address")
    address_apt_suite = models.CharField(max_length=100, blank=True, null=True, help_text="Apartment/Suite number")
    address_city = models.CharField(max_length=100, blank=True, null=True, help_text="City")
    address_state = models.CharField(max_length=100, blank=True, null=True, help_text="State/Province")
    address_zip = models.CharField(max_length=20, blank=True, null=True, help_text="ZIP/Postal code")
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Customer Profile'
        verbose_name_plural = 'Customer Profiles'

    def __str__(self):
        return f"Customer Profile for {self.user.email}"


class CustomerTag(models.Model):
    name = models.CharField(max_length=50)
    description = models.TextField(blank=True)
    color = models.CharField(max_length=7, default='#000000')  # Hex color code
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Customer Tag'
        verbose_name_plural = 'Customer Tags'

    def __str__(self):
        return self.name


class CustomerFile(models.Model):
    customer = models.ForeignKey(CustomerProfile, on_delete=models.CASCADE, related_name='files')
    title = models.CharField(max_length=255)
    file = models.FileField(upload_to='customer_files/')
    description = models.TextField(blank=True)
    uploaded_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Customer File'
        verbose_name_plural = 'Customer Files'

    def __str__(self):
        return f"{self.title} - {self.customer.user.email}"


class SignedForm(models.Model):
    customer = models.ForeignKey(CustomerProfile, on_delete=models.CASCADE, related_name='signed_forms')
    title = models.CharField(max_length=255)
    form_type = models.CharField(max_length=100)
    content = models.TextField()
    signature = models.TextField()
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    signed_at = models.DateTimeField(auto_now_add=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Signed Form'
        verbose_name_plural = 'Signed Forms'

    def __str__(self):
        return f"{self.title} - {self.customer.user.email}"


class UploadedFile(models.Model):
    """Model for uploaded files with processing status tracking"""
    
    STATUS_CHOICES = [
        ('uploaded', 'Uploaded'),
        ('queued', 'Queued'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]
    
    file_id = models.CharField(max_length=50, unique=True)
    file = models.FileField(upload_to=upload_file_path)
    file_name = models.CharField(max_length=255)
    file_size = models.IntegerField()
    file_type = models.CharField(max_length=50, default='customer_import')
    description = models.TextField(blank=True)
    skip_duplicates = models.BooleanField(default=True)
    update_existing = models.BooleanField(default=False)
    uploaded_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    
    # Status tracking fields
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='uploaded')
    total_rows = models.IntegerField(null=True, blank=True)
    processed_rows = models.IntegerField(default=0)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        verbose_name = 'Uploaded File'
        verbose_name_plural = 'Uploaded Files'
        ordering = ['-uploaded_at']
    
    def __str__(self):
        return f"{self.file_name} ({self.file_id})"
    
    @property
    def file_path(self):
        """Return the file path"""
        if self.file:
            return self.file.url
        return ""
    
    @property
    def progress(self):
        """Calculate progress percentage"""
        if not self.total_rows or self.total_rows == 0:
            return 0
        return min(100, int((self.processed_rows / self.total_rows) * 100))
    
    def get_estimated_completion(self):
        """Calculate estimated completion time"""
        if not self.started_at or not self.total_rows or self.processed_rows == 0:
            return None
        
        elapsed = timezone.now() - self.started_at
        if self.processed_rows >= self.total_rows:
            return None
        
        remaining_rows = self.total_rows - self.processed_rows
        avg_time_per_row = elapsed.total_seconds() / self.processed_rows
        estimated_remaining_seconds = remaining_rows * avg_time_per_row
        
        return timezone.now() + timedelta(seconds=estimated_remaining_seconds)


class CustomerBookingSession(models.Model):
    """
    Model to store customer booking session data in the backend
    This replaces frontend sessionStorage to prevent cross-contamination
    and enable data persistence across login sessions
    """
    STEP_CHOICES = [
        ('service-selection', 'Service Selection'),
        ('time-selection', 'Time Selection'),
        ('consent', 'Consent'),
        ('review', 'Review'),
        ('confirmation', 'Confirmation'),
    ]

    CONSENT_STATUS_CHOICES = [
        ('not_signed', 'Not Signed'),
        ('signed', 'Signed'),
        ('expired', 'Expired'),
    ]

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='booking_sessions'
    )
    business = models.ForeignKey(
        'business.Business',
        on_delete=models.CASCADE,
        related_name='customer_booking_sessions'
    )
    session_key = models.CharField(
        max_length=40,
        null=True,
        blank=True,
        db_index=True,
        help_text='Django session key for anonymous sessions'
    )
    booking_data = models.JSONField(
        default=dict,
        help_text='Complete booking data including service, employee, date, time, customer info, etc.'
    )
    current_step = models.CharField(
        max_length=20,
        choices=STEP_CHOICES,
        default='service-selection'
    )

    consent_status = models.CharField(
        max_length=20,
        choices=CONSENT_STATUS_CHOICES,
        default='not_signed',
        help_text='Status of consent form signature'
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField(
        help_text='When this booking session expires'
    )
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)

    class Meta:
        verbose_name = 'Customer Booking Session'
        verbose_name_plural = 'Customer Booking Sessions'
        ordering = ['-updated_at']
        unique_together = ('user', 'business')
        indexes = [
            models.Index(fields=['user', 'business']),
            models.Index(fields=['session_key']),
            models.Index(fields=['expires_at']),
            models.Index(fields=['user', 'business', 'current_step']),
        ]

    def __str__(self):
        return f"Booking session for {self.user.email} - {self.business.name}"

    def save(self, *args, **kwargs):
        # Set expiration time if not set (24 hours from now)
        if not self.expires_at:
            self.expires_at = timezone.now() + timedelta(hours=24)
        super().save(*args, **kwargs)

    @property
    def is_expired(self):
        """Check if the booking session has expired"""
        return timezone.now() > self.expires_at

    def extend_expiration(self, hours=24):
        """Extend the expiration time"""
        self.expires_at = timezone.now() + timedelta(hours=hours)
        self.save(update_fields=['expires_at', 'updated_at'])

    def mark_consent_signed(self):
        """Mark consent as signed - this status should not be changed by other users"""
        if self.consent_status != 'signed':
            self.consent_status = 'signed'
            self.save(update_fields=['consent_status'])
            print(f"✅ Marked consent as SIGNED for user {self.user.email}")

    def is_consent_signed(self):
        """Check if consent is signed"""
        return self.consent_status == 'signed'

    @classmethod
    def cleanup_expired_sessions(cls):
        """Remove expired booking sessions"""
        expired_count = cls.objects.filter(expires_at__lt=timezone.now()).count()
        cls.objects.filter(expires_at__lt=timezone.now()).delete()
        return expired_count


def get_customer_email(self, obj):
    return obj.user.email


def get_customer_name(self, obj):
    user = obj.user
    return f"{user.first_name} {user.last_name}".strip() or user.email