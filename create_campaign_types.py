#!/usr/bin/env python
"""
Create Campaign Types

This script creates the default campaign types in the database.
Run with: python create_campaign_types.py
"""

import os
import sys
import django

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

try:
    django.setup()
except ImportError:
    # Try alternative settings path
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
    django.setup()

from marketing.models import CampaignType

# Define the campaign types to create
campaign_types = [
    {
        'name': 'Email Blast',
        'description': 'Email Blast, by default',
        'supports_offset': False,
        'offset_unit': None,
        'min_offset': None,
        'max_offset': None,
        'is_system': True
    },
    {
        'name': 'Text Blast',
        'description': 'Text Blast',
        'supports_offset': False,
        'offset_unit': None,
        'min_offset': None,
        'max_offset': None,
        'is_system': True
    },
    {
        'name': 'Campaign Blast',
        'description': 'Email & Text Blast',
        'supports_offset': False,
        'offset_unit': None,
        'min_offset': None,
        'max_offset': None,
        'is_system': True
    },
    {
        'name': 'Birthday',
        'description': 'Birthday Campaign',
        'supports_offset': True,
        'offset_unit': 'days',
        'min_offset': 0,
        'max_offset': 30,
        'is_system': True
    },
    {
        'name': 'Lost Customer',
        'description': 'Lost Customer Callback',
        'supports_offset': True,
        'offset_unit': 'weeks',
        'min_offset': 1,
        'max_offset': 24,
        'is_system': True
    },
    {
        'name': 'Before Visit',
        'description': 'Before Visit',
        'supports_offset': True,
        'offset_unit': 'days',
        'min_offset': 1,
        'max_offset': 30,
        'is_system': True
    },
    {
        'name': 'After Visit',
        'description': 'After Visit',
        'supports_offset': True,
        'offset_unit': 'days',
        'min_offset': 0,
        'max_offset': 30,
        'is_system': True
    }
]

print("Creating campaign types...")

# Create the campaign types
for ct_data in campaign_types:
    ct, created = CampaignType.objects.get_or_create(
        name=ct_data['name'],
        defaults=ct_data
    )
    if created:
        print(f"Created: ID: {ct.id}, Name: {ct.name}")
    else:
        print(f"Already exists: ID: {ct.id}, Name: {ct.name}")

print("\nCampaign types setup complete!")
print("Available campaign type IDs:")
for ct in CampaignType.objects.all():
    print(f"ID: {ct.id}, Name: {ct.name}")
