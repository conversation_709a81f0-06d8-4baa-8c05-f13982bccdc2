"""
Outbox pattern implementation for reliable event publishing.
Ensures transactional consistency between domain operations and event publishing.
"""

import logging
from typing import Dict, Any, Optional, List
from django.db import transaction
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.cache import cache
from .models import OutboxEvent

User = get_user_model()
logger = logging.getLogger(__name__)


class OutboxService:
    """
    Service for managing outbox events with transactional guarantees.
    Provides methods to create events atomically with domain operations.
    """
    
    @staticmethod
    def create_event(
        event_type: str,
        aggregate_type: str,
        aggregate_id: str,
        payload: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None,
        scheduled_at: Optional[timezone.datetime] = None,
        max_retries: int = 3
    ) -> OutboxEvent:
        """
        Create an outbox event.
        
        Args:
            event_type: Type of event (e.g., 'appointment.created')
            aggregate_type: Type of aggregate (e.g., 'appointment')
            aggregate_id: ID of the aggregate
            payload: Event payload data
            metadata: Additional metadata (user_id, correlation_id, etc.)
            scheduled_at: When to process the event (defaults to now)
            max_retries: Maximum retry attempts
            
        Returns:
            Created OutboxEvent instance
        """
        return OutboxEvent.objects.create(
            event_type=event_type,
            aggregate_type=aggregate_type,
            aggregate_id=str(aggregate_id),
            payload=payload,
            metadata=metadata or {},
            scheduled_at=scheduled_at or timezone.now(),
            max_retries=max_retries
        )
    
    @staticmethod
    @transaction.atomic
    def create_appointment_event_consolidated(
        appointment,
        event_type: str,
        user_context: Optional[User] = None,
        additional_data: Optional[Dict[str, Any]] = None,
        consolidation_window_seconds: int = 5
    ) -> OutboxEvent:
        """
        Create or update an appointment event with consolidation to prevent duplicate events.

        If an event for the same appointment was created within the consolidation window,
        this will update the existing event instead of creating a new one.

        Args:
            appointment: Appointment instance
            event_type: Type of event (e.g., 'appointment.changed')
            user_context: User who triggered the event
            additional_data: Additional event data
            consolidation_window_seconds: Time window for consolidating events

        Returns:
            Created or updated OutboxEvent instance
        """
        cache_key = f"appointment_event_{appointment.id}_{event_type}"

        # Check if there's a recent event for this appointment
        recent_event_id = cache.get(cache_key)

        if recent_event_id:
            try:
                # Try to update the existing event
                existing_event = OutboxEvent.objects.get(
                    id=recent_event_id,
                    status='pending'  # Only update if not yet processed
                )

                # Consolidate with existing event, preserving original "old" values
                updated_payload = OutboxService._consolidate_appointment_payload(
                    existing_event.payload, appointment, additional_data
                )

                existing_event.payload = updated_payload
                existing_event.created_at = timezone.now()  # Update timestamp
                existing_event.save()

                logger.info(f"Consolidated appointment event {existing_event.id} for appointment {appointment.id}")
                return existing_event

            except OutboxEvent.DoesNotExist:
                # Event was already processed or deleted, create new one
                pass

        # Create new event
        event = OutboxService.create_appointment_event(
            appointment=appointment,
            event_type=event_type,
            user_context=user_context,
            additional_data=additional_data
        )

        # Cache the event ID for consolidation
        cache.set(cache_key, event.id, timeout=consolidation_window_seconds)

        return event

    @staticmethod
    def create_appointment_event(
        appointment,
        event_type: str,
        user_context: Optional[User] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> OutboxEvent:
        """
        Create an appointment-related outbox event atomically.
        
        Args:
            appointment: Appointment instance
            event_type: Type of event (e.g., 'appointment.created')
            user_context: User who triggered the event
            additional_data: Additional data to include in payload
            
        Returns:
            Created OutboxEvent instance
        """
        payload = OutboxService._build_appointment_payload(appointment, additional_data)
        
        # Build metadata
        metadata = {
            'timestamp': timezone.now().isoformat(),
            'version': '1.0',
        }
        
        if user_context:
            metadata['triggered_by'] = {
                'user_id': str(user_context.id),
                'email': user_context.email,
            }
        
        return OutboxService.create_event(
            event_type=event_type,
            aggregate_type='appointment',
            aggregate_id=str(appointment.id),
            payload=payload,
            metadata=metadata
        )
    
    @staticmethod
    @transaction.atomic
    def create_waitlist_event(
        waitlist_entry,
        event_type: str,
        user_context: Optional[User] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> OutboxEvent:
        """
        Create a waitlist-related outbox event atomically.
        
        Args:
            waitlist_entry: WaitlistEntry instance
            event_type: Type of event (e.g., 'waitlist.added')
            user_context: User who triggered the event
            additional_data: Additional data to include in payload
            
        Returns:
            Created OutboxEvent instance
        """
        # Build event payload
        payload = {
            'waitlist_entry_id': waitlist_entry.id,
            'customer': {
                'user_id': f"waitlist_{waitlist_entry.id}",  # Generate unique identifier for waitlist customer
                'name': waitlist_entry.customer_name,
                'email': str(waitlist_entry.email),
                'phone': str(waitlist_entry.phone_number),
                'first_name': waitlist_entry.customer_name.split(' ')[0] if waitlist_entry.customer_name else '',
                'last_name': ' '.join(waitlist_entry.customer_name.split(' ')[1:]) if waitlist_entry.customer_name and len(waitlist_entry.customer_name.split(' ')) > 1 else '',
            },
            'business': {
                'id': waitlist_entry.business.id,
                'name': waitlist_entry.business.name,
                'phone': str(getattr(waitlist_entry.business, 'phone', '')),
                'email': str(getattr(waitlist_entry.business, 'email', '')),
            },
            'waitlist_data': {
                'services': [service.name for service in waitlist_entry.services.all()],
                'preferred_times': [{'start': window.start_datetime, 'end': window.end_datetime} for window in waitlist_entry.windows.all()],
                'notes': waitlist_entry.notes,
                'created_at': waitlist_entry.created_at.isoformat(),
                'status': waitlist_entry.status,
                'total_price': waitlist_entry.total_price,
                'total_duration': waitlist_entry.total_duration,
            }
        }
        
        # Add any additional data
        if additional_data:
            payload.update(additional_data)
        
        # Build metadata
        metadata = {
            'timestamp': timezone.now().isoformat(),
            'version': '1.0',
        }
        
        if user_context:
            metadata['triggered_by'] = {
                'user_id': str(user_context.id),
                'email': user_context.email,
            }
        
        return OutboxService.create_event(
            event_type=event_type,
            aggregate_type='waitlist',
            aggregate_id=str(waitlist_entry.id),
            payload=payload,
            metadata=metadata
        )
    
    @staticmethod
    @transaction.atomic
    def create_direct_notification_event(
        user: User,
        message: str,
        notification_type: str = 'direct',
        additional_data: Optional[Dict[str, Any]] = None,
        user_context: Optional[User] = None
    ) -> OutboxEvent:
        """
        Create a direct notification event.
        
        Args:
            user: User to notify
            message: Notification message
            notification_type: Type of notification
            additional_data: Additional data to include
            user_context: User who triggered the notification
            
        Returns:
            Created OutboxEvent instance
        """
        # Build event payload
        payload = {
            'recipient': {
                'user_id': str(user.id),
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
            },
            'message': message,
            'notification_type': notification_type,
        }
        
        # Add any additional data
        if additional_data:
            payload.update(additional_data)
        
        # Build metadata
        metadata = {
            'timestamp': timezone.now().isoformat(),
            'version': '1.0',
        }
        
        if user_context:
            metadata['triggered_by'] = {
                'user_id': str(user_context.id),
                'email': user_context.email,
            }
        
        return OutboxService.create_event(
            event_type='notification.direct',
            aggregate_type='notification',
            aggregate_id=f"user_{user.id}_{timezone.now().timestamp()}",
            payload=payload,
            metadata=metadata
        )
    
    @staticmethod
    def get_pending_events(limit: int = 100) -> List[OutboxEvent]:
        """
        Get pending events ready for processing.
        
        Args:
            limit: Maximum number of events to return
            
        Returns:
            List of OutboxEvent instances ready for processing
        """
        return list(
            OutboxEvent.objects.filter(
                status__in=['pending', 'failed'],
                scheduled_at__lte=timezone.now()
            ).order_by('sequence_number')[:limit]
        )
    
    @staticmethod
    def get_events_by_status(status: str, limit: int = 100) -> List[OutboxEvent]:
        """
        Get events by status.
        
        Args:
            status: Event status to filter by
            limit: Maximum number of events to return
            
        Returns:
            List of OutboxEvent instances
        """
        return list(
            OutboxEvent.objects.filter(status=status)
            .order_by('-created_at')[:limit]
        )
    
    @staticmethod
    def cleanup_processed_events(older_than_days: int = 7) -> int:
        """
        Clean up processed events older than specified days.
        
        Args:
            older_than_days: Delete events older than this many days
            
        Returns:
            Number of events deleted
        """
        cutoff_date = timezone.now() - timezone.timedelta(days=older_than_days)
        
        deleted_count, _ = OutboxEvent.objects.filter(
            status='published',
            processed_at__lt=cutoff_date
        ).delete()
        
        logger.info(f"Cleaned up {deleted_count} processed outbox events older than {older_than_days} days")
        return deleted_count

    @staticmethod
    def _build_appointment_payload(appointment, additional_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Build the complete appointment payload for outbox events.

        Args:
            appointment: Appointment instance
            additional_data: Additional data to include in payload

        Returns:
            Complete appointment payload dictionary
        """
        from django.utils import timezone as django_timezone

        # Ensure times are in local timezone
        start_time = appointment.start_time
        if start_time.tzinfo is None:
            start_time = django_timezone.make_aware(start_time)

        end_time = appointment.end_time
        if end_time and end_time.tzinfo is None:
            end_time = django_timezone.make_aware(end_time)

        payload = {
            'appointment_id': appointment.id,
            'customer_id': appointment.customer.id if appointment.customer else None,
            'employee_id': appointment.employee.id if appointment.employee else None,
            'business_id': appointment.business.id if appointment.business else None,
            'start_time': start_time.astimezone(django_timezone.get_current_timezone()).isoformat(),
            'end_time': end_time.astimezone(django_timezone.get_current_timezone()).isoformat() if end_time else None,
            'total_duration': appointment.total_duration,
            'status': appointment.status,
            'source': appointment.source,
        }

        # Add customer and employee details for notifications
        if appointment.customer and appointment.customer.customer:
            customer_user = appointment.customer.customer.user
            payload['customer'] = {
                'user_id': str(customer_user.id),
                'email': customer_user.email,
                'first_name': customer_user.first_name,
                'last_name': customer_user.last_name,
            }

        if appointment.employee and appointment.employee.user:
            employee_user = appointment.employee.user
            employee_title = appointment.employee.stylist_level.name if appointment.employee.stylist_level else "Staff"
            payload['employee'] = {
                'user_id': str(employee_user.id),
                'email': employee_user.email,
                'first_name': employee_user.first_name,
                'last_name': employee_user.last_name,
                'title': employee_title,
            }

        # Add current services and addons (id and name only)
        payload['appointment_services'] = [
            {
                'id': service.id,
                'name': service.service.name,
            } for service in appointment.appointment_services.all()
        ]

        payload['appointment_addons'] = [
            {
                'id': addon.id,
                'name': addon.add_on.name,
            } for addon in appointment.appointment_add_ons.all()
        ]

        # Add any additional data
        if additional_data:
            payload.update(additional_data)

        return payload

    @staticmethod
    def _consolidate_appointment_payload(
        existing_payload: Dict[str, Any],
        appointment,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Consolidate appointment payload, preserving original "old" values.

        Args:
            existing_payload: Payload from the existing event
            appointment: Current appointment instance
            additional_data: New additional data to merge

        Returns:
            Consolidated payload with preserved "old" values and updated "new" values
        """
        # Start with fresh payload for current state
        new_payload = OutboxService._build_appointment_payload(appointment, additional_data)

        # Preserve original "old" values from the existing event
        preserved_old_fields = [
            'old_start_time',
            'old_appointment_services',
            'old_appointment_addons'
        ]

        for field in preserved_old_fields:
            if field in existing_payload:
                # Keep the original "old" value, but normalize format if needed
                old_value = existing_payload[field]

                # Handle format conversion for services/addons
                if field in ['old_appointment_services', 'old_appointment_addons']:
                    old_value = OutboxService._normalize_service_addon_format(old_value)

                new_payload[field] = old_value

        # For fields that might not have been in the original event,
        # use the current values as "old" if they weren't already set
        if 'old_start_time' not in new_payload:
            # This is the first change, use current time as both old and new
            from django.utils import timezone as django_timezone
            current_time = appointment.start_time.astimezone(django_timezone.get_current_timezone()).isoformat()
            new_payload['old_start_time'] = current_time

        if 'old_appointment_services' not in new_payload:
            # Use current services as old if not set
            current_services = [service.service.name for service in appointment.appointment_services.all()]
            new_payload['old_appointment_services'] = current_services

        if 'old_appointment_addons' not in new_payload:
            # Use current addons as old if not set
            current_addons = [addon.add_on.name for addon in appointment.appointment_add_ons.all()]
            new_payload['old_appointment_addons'] = current_addons

        logger.debug(f"Consolidated payload: preserved old values, updated new values")
        return new_payload

    @staticmethod
    def _normalize_service_addon_format(value):
        """
        Normalize service/addon format to simple string arrays.

        Handles conversion from old format (objects with id, name, etc.)
        to new format (simple name strings).

        Args:
            value: Service/addon array in any format

        Returns:
            Normalized array of name strings
        """
        if not isinstance(value, list):
            return value

        normalized = []
        for item in value:
            if isinstance(item, dict) and 'name' in item:
                # Old format: {"id": 1, "name": "Service Name", ...}
                normalized.append(item['name'])
            elif isinstance(item, str):
                # New format: "Service Name"
                normalized.append(item)
            else:
                # Unknown format, keep as-is
                normalized.append(item)

        return normalized
