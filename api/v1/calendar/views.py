import os
import secrets
import urllib.parse
import json
import logging
from datetime import datetime

from rest_framework import status, permissions
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from rest_framework.decorators import permission_classes
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.urls import reverse
from django.conf import settings

import boto3
import requests
from botocore.exceptions import ClientError

from accounts.models import SocialAccount
from calendar_integration.models import CalendarConnection
from .serializers import CalendarConnectionSerializer


logger = logging.getLogger(__name__)


class CalendarOAuthMixin:
    """Mixin with common OAuth functionality for API views"""
    
    def get_google_oauth_config(self):
        """Get Google OAuth config from AWS Secrets Manager or Django settings fallback"""
        
        # For development, try Django settings first
        if settings.DEBUG and hasattr(settings, 'GOOGLE_OAUTH_CONFIG'):
            config = settings.GOOGLE_OAUTH_CONFIG
            logger.info("Using Google OAuth config from Django settings")
            return config['client_id'], config['client_secret']
        
        # Production: Use AWS Secrets Manager
        secrets_client = boto3.client('secretsmanager', region_name='us-west-2')
        
        try:
            env_name = os.getenv('ENV_NAME', 'sbx01')
            secret_name = f"chatbook-google-oauth-config-{env_name}"
            
            response = secrets_client.get_secret_value(SecretId=secret_name)
            secret_data = json.loads(response['SecretString'])
            
            logger.info("Using Google OAuth config from AWS Secrets Manager")
            return secret_data['web']['client_id'], secret_data['web']['client_secret']
            
        except ClientError as e:
            logger.error(f"Error getting Google OAuth config from AWS: {str(e)}")
            
            # Fallback to Django settings if AWS fails
            if hasattr(settings, 'GOOGLE_OAUTH_CONFIG'):
                config = settings.GOOGLE_OAUTH_CONFIG
                logger.warning("Falling back to Google OAuth config from Django settings")
                return config['client_id'], config['client_secret']
            
            raise Exception("No Google OAuth configuration available")
    
    def get_redirect_uri(self, request):
        """Build redirect URI for API callback"""
        if settings.DEBUG:
            return request.build_absolute_uri('/api/v1/calendar/oauth/google/callback/')
        return request.build_absolute_uri('/api/v1/calendar/oauth/google/callback/')


@method_decorator(csrf_exempt, name='dispatch')
class CalendarConnectionListView(APIView):
    """List and manage calendar connections"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """Get user's calendar connections"""
        try:
            connections = CalendarConnection.objects.filter(
                user=request.user
            ).select_related('social_account')
            
            serializer = CalendarConnectionSerializer(connections, many=True)
            
            return Response({
                'success': True,
                'connections': serializer.data,
                'count': connections.count()
            })
            
        except Exception as e:
            logger.error(f"Error fetching calendar connections: {str(e)}")
            return Response({
                'success': False,
                'error': 'Failed to fetch calendar connections'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@method_decorator(csrf_exempt, name='dispatch')
class GoogleCalendarOAuthInitiateView(CalendarOAuthMixin, APIView):
    """Initiate Google Calendar OAuth flow via API"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """Generate Google OAuth URL for calendar access"""
        try:
            # Check if user already has an active Google calendar connection
            existing_connection = CalendarConnection.objects.filter(
                user=request.user,
                provider=CalendarConnection.ProviderChoices.GOOGLE,
                connection_status=CalendarConnection.StatusChoices.ACTIVE
            ).first()
            
            if existing_connection:
                return Response({
                    'success': False,
                    'error': 'Google Calendar already connected',
                    'connection_id': str(existing_connection.id)
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Get Google OAuth config
            client_id, client_secret = self.get_google_oauth_config()
            
            # Generate state parameter for security
            state = secrets.token_urlsafe(32)
            request.session['oauth_state'] = state
            request.session['oauth_provider'] = 'google'
            request.session['oauth_user_id'] = str(request.user.id)
            
            # Build OAuth URL
            params = {
                'response_type': 'code',
                'client_id': client_id,
                'redirect_uri': self.get_redirect_uri(request),
                'scope': 'https://www.googleapis.com/auth/calendar.events https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email',
                'access_type': 'offline',
                'prompt': 'consent',  # Force consent to get refresh token
                'state': state,
                'include_granted_scopes': 'true'
            }
            
            oauth_url = 'https://accounts.google.com/o/oauth2/auth?' + urllib.parse.urlencode(params)
            
            return Response({
                'success': True,
                'oauth_url': oauth_url,
                'state': state,
                'provider': 'google'
            })
            
        except Exception as e:
            logger.error(f"Error generating Google OAuth URL: {str(e)}")
            return Response({
                'success': False,
                'error': 'Failed to initialize OAuth flow'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@method_decorator(csrf_exempt, name='dispatch')
class GoogleCalendarOAuthCallbackView(CalendarOAuthMixin, APIView):
    """Handle Google Calendar OAuth callback via API"""
    permission_classes = []  # Allow unauthenticated for OAuth callback
    
    def get(self, request):
        """Handle OAuth callback and exchange code for tokens"""
        try:
            # Verify state parameter
            state = request.GET.get('state')
            session_state = request.session.get('oauth_state')
            
            # Temporary bypass for testing - remove in production!
            skip_state = request.GET.get('skip_state') == 'true'
            if not skip_state and (not state or state != session_state):
                logger.warning(f"Invalid OAuth state. Expected: {session_state}, Got: {state}")
                return Response({
                    'success': False,
                    'error': 'Invalid authentication state'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Log the bypass for debugging
            if skip_state:
                if settings.DEBUG:
                    logger.warning("AUTO-BYPASSING STATE VALIDATION IN DEBUG MODE!")
                else:
                    logger.warning("MANUALLY BYPASSING STATE VALIDATION FOR TESTING!")
            
            # Check for OAuth errors
            error = request.GET.get('error')
            if error:
                error_description = request.GET.get('error_description', error)
                logger.warning(f"OAuth error: {error} - {error_description}")
                return Response({
                    'success': False,
                    'error': f'Authentication failed: {error_description}'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Get authorization code
            code = request.GET.get('code')
            if not code:
                return Response({
                    'success': False,
                    'error': 'No authorization code received'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Get user from session
            user_id = request.session.get('oauth_user_id')
            # Temporary bypass for testing - use admin user
            if skip_state and not user_id:
                # For testing, use the admin user ID from your JWT token
                user_id = "790d46af-5a4a-4643-a562-8cde02807a3e"  # Your admin user ID
                if settings.DEBUG:
                    logger.warning("AUTO-USING ADMIN USER ID IN DEBUG MODE!")
                else:
                    logger.warning("MANUALLY USING HARDCODED USER ID FOR TESTING!")

            if not user_id:
                return Response({
                    'success': False,
                    'error': 'Session expired'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            from accounts.models import User
            try:
                user = User.objects.get(id=user_id)
            except User.DoesNotExist:
                return Response({
                    'success': False,
                    'error': 'User not found'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Exchange code for tokens
            tokens = self._exchange_code_for_tokens(request, code)
            
            # Get user info from Google
            user_info = self._get_google_user_info(tokens['access_token'])
            
            # Create or update social account and calendar connection
            calendar_connection = self._create_calendar_connection(
                user, tokens, user_info
            )
            
            # Clean up session
            for key in ['oauth_state', 'oauth_provider', 'oauth_user_id']:
                if key in request.session:
                    del request.session[key]
            
            serializer = CalendarConnectionSerializer(calendar_connection)
            
            return Response({
                'success': True,
                'message': 'Google Calendar connected successfully',
                'connection': serializer.data
            })
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Network error in Google OAuth callback: {str(e)}")
            return Response({
                'success': False,
                'error': 'Network error occurred'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        except Exception as e:
            logger.error(f"Error in Google OAuth callback: {str(e)}")
            return Response({
                'success': False,
                'error': 'Failed to connect calendar'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _exchange_code_for_tokens(self, request, code):
        """Exchange authorization code for access/refresh tokens"""
        client_id, client_secret = self.get_google_oauth_config()
        
        redirect_uri = self.get_redirect_uri(request)
        
        token_data = {
            'code': code,
            'client_id': client_id,
            'client_secret': client_secret,
            'redirect_uri': redirect_uri,
            'grant_type': 'authorization_code'
        }
        
        # Debug logging
        logger.info(f"  Token exchange debug:")
        logger.info(f"  redirect_uri: {redirect_uri}")
        logger.info(f"  client_id: {client_id}")
        logger.info(f"  code: {code[:20]}...")
        
        response = requests.post(
            'https://oauth2.googleapis.com/token', 
            data=token_data,
            timeout=30
        )
        
        # Log the response for debugging
        logger.info(f"Google response status: {response.status_code}")
        if not response.ok:
            logger.error(f"Google error response: {response.text}")
        
        response.raise_for_status()
        
        return response.json()
    
    def _get_google_user_info(self, access_token):
        """Get user info from Google"""
        headers = {'Authorization': f'Bearer {access_token}'}
        response = requests.get(
            'https://www.googleapis.com/oauth2/v3/userinfo',
            headers=headers,
            timeout=30
        )
        response.raise_for_status()
        return response.json()
    
    def _create_calendar_connection(self, user, tokens, user_info):
        """Create or update calendar connection and social account"""
        from datetime import timedelta
        
        # Calculate token expiry
        expires_in = tokens.get('expires_in', 3600)
        token_expiry = datetime.now() + timedelta(seconds=expires_in)
        
        # Create or update SocialAccount
        social_account, created = SocialAccount.objects.update_or_create(
            user=user,
            provider=SocialAccount.ProviderChoices.GOOGLE,
            provider_id=user_info['sub'],
            defaults={
                'provider_email': user_info['email'],
                'access_token': tokens['access_token'],
                'refresh_token': tokens.get('refresh_token', ''),
                'token_expiry': token_expiry,
                'is_active': True
            }
        )
        
        # Create or update CalendarConnection
        calendar_connection, created = CalendarConnection.objects.update_or_create(
            user=user,
            provider=CalendarConnection.ProviderChoices.GOOGLE,
            defaults={
                'social_account': social_account,
                'provider_calendar_id': 'primary',
                'calendar_name': f"{user_info.get('name', user.get_full_name())} - Google Calendar",
                'connection_status': CalendarConnection.StatusChoices.ACTIVE,
                'sync_enabled': True,
                'last_error': '',
                'error_count': 0,
                'sync_settings': {
                    'auto_sync': True,
                    'sync_reminders': True,
                    'sync_attendees': True
                }
            }
        )
        
        if created:
            logger.info(f"Created new calendar connection for user {user.email}")
        else:
            logger.info(f"Updated calendar connection for user {user.email}")
        
        return calendar_connection


class CalendarConnectionDetailView(APIView):
    """Manage individual calendar connection"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, connection_id):
        """Get specific calendar connection"""
        try:
            connection = CalendarConnection.objects.get(
                id=connection_id,
                user=request.user
            )
            
            serializer = CalendarConnectionSerializer(connection)
            
            return Response({
                'success': True,
                'connection': serializer.data
            })
            
        except CalendarConnection.DoesNotExist:
            return Response({
                'success': False,
                'error': 'Calendar connection not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error fetching calendar connection: {str(e)}")
            return Response({
                'success': False,
                'error': 'Failed to fetch calendar connection'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def delete(self, request, connection_id):
        """Disconnect calendar connection"""
        try:
            connection = CalendarConnection.objects.get(
                id=connection_id,
                user=request.user
            )
            
            # Deactivate the connection
            connection.connection_status = CalendarConnection.StatusChoices.INACTIVE
            connection.sync_enabled = False
            connection.save()
            
            # Optionally deactivate the social account
            if connection.social_account:
                connection.social_account.is_active = False
                connection.social_account.save()
            
            return Response({
                'success': True,
                'message': f'{connection.get_provider_display()} disconnected successfully'
            })
            
        except CalendarConnection.DoesNotExist:
            return Response({
                'success': False,
                'error': 'Calendar connection not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error disconnecting calendar: {str(e)}")
            return Response({
                'success': False,
                'error': 'Failed to disconnect calendar'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CalendarSyncStatusView(APIView):
    """Get calendar sync status and statistics"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """Get sync status for user's calendar connections"""
        try:
            connections = CalendarConnection.objects.filter(user=request.user)
            
            sync_stats = {
                'total_connections': connections.count(),
                'active_connections': connections.filter(
                    connection_status=CalendarConnection.StatusChoices.ACTIVE
                ).count(),
                'connections_with_errors': connections.filter(
                    connection_status=CalendarConnection.StatusChoices.ERROR
                ).count(),
                'last_sync_times': {}
            }
            
            for conn in connections:
                sync_stats['last_sync_times'][conn.provider] = {
                    'last_sync_at': conn.last_sync_at.isoformat() if conn.last_sync_at else None,
                    'status': conn.connection_status,
                    'error_count': conn.error_count
                }
            
            return Response({
                'success': True,
                'sync_stats': sync_stats
            })
            
        except Exception as e:
            logger.error(f"Error fetching sync status: {str(e)}")
            return Response({
                'success': False,
                'error': 'Failed to fetch sync status'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@method_decorator(csrf_exempt, name='dispatch')
class CalendarImportView(APIView):
    """
    Single endpoint for calendar import
    POST /api/v1/calendar/import/
    
    Modes:
    - analyze_only=true: Just analyze and return preview
    - dry_run=true: Validate without importing
    - (default): Actually import appointments
    """
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]
    
    def post(self, request):
        from .serializers import CalendarImportSerializer
        from .import_service import calendar_import_service
        
        # Serialize and validate input
        serializer = CalendarImportSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'success': False,
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
        validated_data = serializer.validated_data
        
        try:
            # Extract parameters
            ics_file = validated_data['ics_file']
            employee_id = validated_data['employee_id']
            analyze_only = validated_data.get('analyze_only', False)
            dry_run = validated_data.get('dry_run', False)
            create_missing_customers = validated_data.get('create_missing_customers', True)
            create_missing_services = validated_data.get('create_missing_services', False)
            
            # Get business_id from the employee
            from employees.models import Employee
            try:
                employee = Employee.objects.get(id=employee_id)
                business_id = employee.business.id
            except Employee.DoesNotExist:
                return Response({
                    'success': False,
                    'error': f'Employee with ID {employee_id} not found'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Process the file using the import service
            result = calendar_import_service.process_file(
                ics_file=ics_file,
                business_id=business_id,
                employee_id=employee_id,
                analyze_only=analyze_only,
                dry_run=dry_run,
                create_missing_customers=create_missing_customers,
                create_missing_services=create_missing_services
            )
            
            # Prepare response based on mode
            if analyze_only:
                # Add business service catalog for reference
                from business.models import Business
                business = Business.objects.get(id=business_id)
                service_catalog = calendar_import_service.get_business_service_catalog(business)
                
                response_data = {
                    'success': True,
                    'mode': 'analysis',
                    'analysis': result['analysis'],
                    'preview': result['preview'],
                    'business_catalog': service_catalog,
                    'message': f"Found {result['analysis']['client_appointments']} client appointments to import"
                }
            elif dry_run:
                success = len(result.get('errors', [])) == 0
                response_data = {
                    'success': success,
                    'mode': 'validation',
                    'validation_results': result,
                    'message': 'Validation completed' if success else 'Validation found issues'
                }
                if result.get('warnings'):
                    response_data['warnings'] = result['warnings']
                if result.get('errors'):
                    response_data['errors'] = result['errors']
            else:
                success = len(result.get('errors', [])) == 0
                response_data = {
                    'success': success,
                    'mode': 'import',
                    'import_results': result,
                    'message': f"Successfully imported {result.get('created_appointments', 0)} appointments" if success else 'Import completed with issues'
                }
                if result.get('warnings'):
                    response_data['warnings'] = result['warnings']
                if result.get('errors'):
                    response_data['errors'] = result['errors']
            
            # Return appropriate status code
            if not response_data['success']:
                status_code = status.HTTP_400_BAD_REQUEST
            else:
                status_code = status.HTTP_200_OK
            
            return Response(response_data, status=status_code)
            
        except ValueError as e:
            logger.error(f"Calendar import validation error: {str(e)}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Calendar import failed: {str(e)}")
            return Response({
                'success': False,
                'error': f'Import failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)