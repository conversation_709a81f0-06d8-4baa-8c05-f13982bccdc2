#!/usr/bin/env python3
"""
Calendar Import Service
Extracted from create_import_appointments_to_calendar.py script
Provides fuzzy matching and appointment import functionality as a reusable service
"""

import tempfile
import os
from datetime import datetime, timedelta
from collections import defaultdict
from typing import Dict, List, Optional, Tuple
from difflib import SequenceMatcher
import re

from django.db import transaction
from django.utils import timezone
from django.contrib.auth import get_user_model
import pytz

from business.models import Business, BusinessCustomer
from employees.models import Employee
from customers.models import CustomerProfile
from services.models import Service, ServiceCategory, AddOn
from appointments.models import Appointment, AppointmentService, AppointmentAddOn
from accounts.models import User

import logging
logger = logging.getLogger(__name__)


class CalendarImportService:
    """Service for importing ICS calendar files with fuzzy matching"""
    
    def fuzzy_match_service(self, service_text: str, business_services, threshold: float = 0.5) -> Tuple[Optional[int], float]:
        """
        Find the best matching service using fuzzy string matching
        Returns (service_id, confidence_score) or (None, 0) if no good match
        """
        best_match = None
        best_score = 0
        
        # Normalize the input text
        service_text_clean = service_text.lower().strip()
        
        # Try different matching strategies
        for service in business_services:
            service_name_clean = service.name.lower().strip()
            scores = []
            
            # 1. Direct name matching
            scores.append(SequenceMatcher(None, service_text_clean, service_name_clean).ratio())
            
            # 2. Partial matching (both directions)
            if service_text_clean in service_name_clean:
                scores.append(len(service_text_clean) / len(service_name_clean))
            if service_name_clean in service_text_clean:
                scores.append(len(service_name_clean) / len(service_text_clean))
            
            # 3. Word-based matching (split and match individual words)
            service_words = set(service_name_clean.split())
            text_words = set(service_text_clean.split())
            if service_words and text_words:
                word_overlap = len(service_words.intersection(text_words))
                word_union = len(service_words.union(text_words))
                if word_union > 0:
                    scores.append(word_overlap / word_union)
            
            # 4. Category + partial name matching
            if service.category:
                category_name = service.category.name.lower().strip()
                # Try category + first word of service
                service_first_word = service_name_clean.split()[0] if service_name_clean.split() else ''
                combined_text = f"{category_name} {service_first_word}".strip()
                if combined_text:
                    scores.append(SequenceMatcher(None, service_text_clean, combined_text).ratio())
                
                # Try just category matching
                scores.append(SequenceMatcher(None, service_text_clean, category_name).ratio())
            
            # 5. Description matching (if available)
            if hasattr(service, 'description') and service.description:
                desc_clean = service.description.lower().strip()
                scores.append(SequenceMatcher(None, service_text_clean, desc_clean).ratio())
            
            # Take the best score from all strategies
            score = max(scores) if scores else 0
            
            if score > best_score and score >= threshold:
                best_match = service
                best_score = score
        
        return (best_match.id if best_match else None, best_score)

    def fuzzy_match_addon(self, addon_text: str, business_addons, threshold: float = 0.5) -> Tuple[Optional[int], float]:
        """
        Find the best matching add-on using fuzzy string matching
        Returns (addon_id, confidence_score) or (None, 0) if no good match
        """
        best_match = None
        best_score = 0
        
        addon_text_clean = addon_text.lower().strip()
        
        for addon in business_addons:
            addon_name_clean = addon.name.lower().strip()
            scores = []
            
            # 1. Direct name matching
            scores.append(SequenceMatcher(None, addon_text_clean, addon_name_clean).ratio())
            
            # 2. Partial matching
            if addon_text_clean in addon_name_clean:
                scores.append(len(addon_text_clean) / len(addon_name_clean))
            if addon_name_clean in addon_text_clean:
                scores.append(len(addon_name_clean) / len(addon_text_clean))
            
            # 3. Word-based matching
            addon_words = set(addon_name_clean.split())
            text_words = set(addon_text_clean.split())
            if addon_words and text_words:
                word_overlap = len(addon_words.intersection(text_words))
                word_union = len(addon_words.union(text_words))
                if word_union > 0:
                    scores.append(word_overlap / word_union)
            
            # 4. Description matching (if available)
            if hasattr(addon, 'description') and addon.description:
                desc_clean = addon.description.lower().strip()
                scores.append(SequenceMatcher(None, addon_text_clean, desc_clean).ratio())
            
            # Take the best score
            score = max(scores) if scores else 0
            
            if score > best_score and score >= threshold:
                best_match = addon
                best_score = score
        
        return (best_match.id if best_match else None, best_score)

    def get_business_service_catalog(self, business: Business) -> Dict:
        """
        Get a summary of the business's services and add-ons for matching reference
        """
        services = Service.objects.filter(business=business, is_active=True).select_related('category')
        addons = AddOn.objects.filter(business=business, is_active=True)
        
        service_catalog = {
            'business_name': business.name,
            'total_services': services.count(),
            'total_addons': addons.count(),
            'services': [],
            'addons': [],
            'categories': set()
        }
        
        for service in services:
            service_info = {
                'id': service.id,
                'name': service.name,
                'category': service.category.name if service.category else None,
                'duration_minutes': int(service.base_duration.total_seconds() // 60) if service.base_duration else 0,
                'price': float(service.base_price) if service.base_price else 0.0
            }
            service_catalog['services'].append(service_info)
            if service.category:
                service_catalog['categories'].add(service.category.name)
        
        for addon in addons:
            addon_info = {
                'id': addon.id,
                'name': addon.name,
                'duration_minutes': int(addon.base_duration.total_seconds() // 60) if addon.base_duration else 0,
                'price': float(addon.base_price) if addon.base_price else 0.0
            }
            service_catalog['addons'].append(addon_info)
        
        service_catalog['categories'] = sorted(list(service_catalog['categories']))
        return service_catalog

    def parse_ics_file(self, file_path: str) -> List[Dict]:
        """
        Simple ICS file parser to extract events
        """
        logger.info(f"Parsing ICS file: {file_path}")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        events = []
        current_event = {}
        in_event = False
        
        with open(file_path, 'r', encoding='utf-8') as file:
            for line_num, line in enumerate(file, 1):
                line = line.strip()
                
                if line == 'BEGIN:VEVENT':
                    in_event = True
                    current_event = {}
                    continue
                elif line == 'END:VEVENT':
                    in_event = False
                    if current_event:
                        events.append(current_event.copy())
                    current_event = {}
                    continue
                
                if in_event and ':' in line:
                    # Split on first colon to handle timestamps with colons
                    key, value = line.split(':', 1)
                    current_event[key] = value
        
        logger.info(f"Parsed {len(events)} events from ICS file")
        return events

    def extract_email_from_event(self, event: Dict) -> Optional[str]:
        """
        Extract email from ICS event fields (ATTENDEE, ORGANIZER, etc.)
        Returns email if found, None otherwise
        """
        # Check ATTENDEE field (format: "ATTENDEE;CN=John Doe:mailto:<EMAIL>")
        if 'ATTENDEE' in event:
            attendee = event['ATTENDEE']
            if 'mailto:' in attendee:
                email = attendee.split('mailto:')[-1].strip()
                if '@' in email:
                    return email
        
        # Check ORGANIZER field (format: "ORGANIZER;CN=Jane Smith:mailto:<EMAIL>")
        if 'ORGANIZER' in event:
            organizer = event['ORGANIZER']
            if 'mailto:' in organizer:
                email = organizer.split('mailto:')[-1].strip()
                if '@' in email:
                    return email
        
        # Check other possible email fields
        email_fields = ['EMAIL', 'CONTACT', 'X-EMAIL']
        for field in email_fields:
            if field in event and '@' in event[field]:
                return event[field].strip()
        
        return None

    def parse_appointment_summary(self, summary: str) -> Dict:
        """
        Parse the SUMMARY field into structured data
        Returns a dictionary with parsed appointment details
        """
        if not summary or summary.strip() == '':
            return {'type': 'unknown', 'raw': summary}
        
        summary = summary.strip()
        
        # Handle empty or placeholder events
        if summary in ['---', '']:
            return {'type': 'placeholder', 'raw': summary}
        
        # Handle personal events (no customer name)
        personal_events = ['Workout', 'Badminton', 'Badminton lesson', 'Lunch', 'Dental', 'Vision', 
                          'Airport pick up', 'Pick up kids']
        if summary in personal_events:
            return {
                'type': 'personal',
                'activity': summary,
                'raw': summary
            }
        
        # Handle staff transition events (Cait>Carol format)
        if '>' in summary and len(summary.split('>')) == 2:
            from_staff, to_staff = summary.split('>')
            return {
                'type': 'staff_transition',
                'from_staff': from_staff.strip(),
                'to_staff': to_staff.strip(),
                'raw': summary
            }
        
        # Handle client appointments (Customer Name - Service Type)
        if ' - ' in summary:
            parts = summary.split(' - ', 1)
            if len(parts) == 2:
                customer_name = parts[0].strip()
                service_info = parts[1].strip()
                
                # Parse service details (we'll pass business services later when available)
                parsed_service = self.parse_service_details(service_info)
                
                return {
                    'type': 'client_appointment',
                    'customer_name': customer_name,
                    'service': parsed_service,
                    'raw': summary
                }
        
        # Handle special cases or unrecognized format
        return {
            'type': 'other',
            'content': summary,
            'raw': summary
        }

    def parse_service_details(self, service_info: str, business_services=None, business_addons=None) -> Dict:
        """
        Parse service details using generic patterns and fuzzy matching
        Works with any business by anchoring to their actual services
        """
        service_data = {
            'name': service_info,
            'category': None,
            'type': None,
            'add_ons': [],
            'add_on_ids': [],
            'raw': service_info,
            'service_id': None,
            'match_confidence': 0.0,
            'parsed_tokens': []
        }
        
        # Generic patterns for common appointment text structures
        cleaned_service = service_info.strip()
        
        # Extract add-ons using generic patterns (with/w/, +, etc.)
        addon_patterns = [
            r'with\s+([^,]+?)(?:,|$)',  # "with Something"
            r'w/\s*([^,]+?)(?:,|$)',    # "w/ Something"  
            r'\+\s*([^,]+?)(?:,|$)',    # "+ Something"
            r'&\s*([^,]+?)(?:,|$)',     # "& Something"
        ]
        
        for pattern in addon_patterns:
            matches = re.findall(pattern, cleaned_service, re.IGNORECASE)
            for match in matches:
                addon_text = match.strip()
                if business_addons:
                    addon_id, confidence = self.fuzzy_match_addon(addon_text, business_addons)
                    if addon_id and confidence > 0.5:  # Only add if good match
                        service_data['add_ons'].append(addon_text)
                        service_data['add_on_ids'].append(addon_id)
                else:
                    service_data['add_ons'].append(addon_text)
                
                # Remove the addon text to get base service
                cleaned_service = re.sub(pattern, '', cleaned_service, flags=re.IGNORECASE).strip()
        
        # Clean up common separators and whitespace
        cleaned_service = re.sub(r'\s*[-–—]\s*', ' ', cleaned_service)  # Replace dashes
        cleaned_service = re.sub(r'\s+', ' ', cleaned_service).strip()  # Normalize whitespace
        
        # Extract common service descriptors using generic patterns
        descriptors = []
        
        # Time-based patterns (30min, 1hour, etc.)
        time_matches = re.findall(r'(\d+(?:\.\d+)?)\s*(min|minute|minutes|hr|hour|hours|h)\b', cleaned_service, re.IGNORECASE)
        for time_val, time_unit in time_matches:
            descriptors.append(f"{time_val}{time_unit}")
        
        # Price patterns ($50, £30, etc.)
        price_matches = re.findall(r'([£$€¥]\d+(?:\.\d{2})?)', cleaned_service, re.IGNORECASE)
        descriptors.extend(price_matches)
        
        # Common service types (could be industry-agnostic)
        type_patterns = [
            r'\b(consultation|consult)\b',
            r'\b(treatment|service)\b', 
            r'\b(appointment|appt)\b',
            r'\b(session)\b',
            r'\b(follow-?up|followup)\b',
            r'\b(initial|first|new)\b',
            r'\b(return|returning|repeat)\b'
        ]
        
        for pattern in type_patterns:
            matches = re.findall(pattern, cleaned_service, re.IGNORECASE)
            descriptors.extend(matches)
        
        service_data['parsed_tokens'] = descriptors
        
        # If we have business services, do fuzzy matching directly on the cleaned text
        if business_services:
            # Try multiple search strategies
            search_candidates = [
                cleaned_service,  # Full cleaned text
                service_info.strip(),  # Original text
            ]
            
            # Also try without common words that might interfere
            noise_words = ['appointment', 'session', 'service', 'treatment', 'with', 'and', 'or', 'the', 'a', 'an']
            filtered_text = cleaned_service
            for word in noise_words:
                filtered_text = re.sub(rf'\b{word}\b', '', filtered_text, flags=re.IGNORECASE)
            filtered_text = re.sub(r'\s+', ' ', filtered_text).strip()
            if filtered_text:
                search_candidates.append(filtered_text)
            
            best_service_id = None
            best_confidence = 0.0
            
            for search_text in search_candidates:
                if not search_text:
                    continue
                    
                service_id, confidence = self.fuzzy_match_service(search_text, business_services)
                if confidence > best_confidence:
                    best_service_id = service_id
                    best_confidence = confidence
            
            service_data['service_id'] = best_service_id
            service_data['match_confidence'] = best_confidence
            
            # If we found a good match, extract the service details
            if best_service_id:
                try:
                    matched_service = next(s for s in business_services if s.id == best_service_id)
                    service_data['name'] = matched_service.name
                    if matched_service.category:
                        service_data['category'] = matched_service.category.name
                except StopIteration:
                    pass
        
        return service_data

    def analyze_parsed_data(self, events: List[Dict], business_services=None, business_addons=None) -> Dict:
        """
        Analyze the parsed appointment data and show statistics
        Enhanced with fuzzy matching results
        """
        logger.info("Analyzing parsed appointment data")
        
        appointment_types = {}
        service_categories = {}
        customers = set()
        add_ons_used = {}
        fuzzy_matches = {'high': 0, 'medium': 0, 'low': 0, 'failed': 0}
        
        client_appointments = []
        
        for event in events:
            summary = event.get('SUMMARY', '')
            parsed = self.parse_appointment_summary(summary)
            
            # Re-parse with business context if available
            if parsed['type'] == 'client_appointment' and business_services:
                service_info = parsed['service']['raw']
                enhanced_service = self.parse_service_details(service_info, business_services, business_addons)
                parsed['service'] = enhanced_service
            
            # Add parsed data to the event
            event['parsed'] = parsed
            
            # Count appointment types
            appointment_types[parsed['type']] = appointment_types.get(parsed['type'], 0) + 1
            
            if parsed['type'] == 'client_appointment':
                client_appointments.append(event)
                customers.add(parsed['customer_name'])
                
                service = parsed['service']
                if service['category']:
                    service_categories[service['category']] = service_categories.get(service['category'], 0) + 1
                
                for add_on in service['add_ons']:
                    add_ons_used[add_on] = add_ons_used.get(add_on, 0) + 1
                
                # Track fuzzy matching performance
                if business_services and service.get('match_confidence'):
                    confidence = service['match_confidence']
                    if confidence >= 0.9:
                        fuzzy_matches['high'] += 1
                    elif confidence >= 0.7:
                        fuzzy_matches['medium'] += 1
                    elif confidence >= 0.5:
                        fuzzy_matches['low'] += 1
                    else:
                        fuzzy_matches['failed'] += 1
                elif business_services:
                    fuzzy_matches['failed'] += 1
        
        # Calculate date range
        date_range = {}
        start_dates = []
        for event in events:
            if 'DTSTART' in event:
                try:
                    dt_str = event['DTSTART']
                    if dt_str.endswith('Z'):
                        dt = datetime.strptime(dt_str, '%Y%m%dT%H%M%SZ')
                        start_dates.append(dt)
                except:
                    pass
        
        if start_dates:
            start_dates.sort()
            date_range = {
                'earliest': start_dates[0].strftime('%Y-%m-%d %H:%M:%S UTC'),
                'latest': start_dates[-1].strftime('%Y-%m-%d %H:%M:%S UTC'),
                'total_days': (start_dates[-1] - start_dates[0]).days + 1
            }
        
        # Prepare sample appointments
        sample_appointments = []
        emails_found = 0
        for event in client_appointments[:5]:  # First 5 client appointments
            parsed = event['parsed']
            extracted_email = self.extract_email_from_event(event)
            if extracted_email:
                emails_found += 1
                
            sample = {
                'customer_name': parsed['customer_name'],
                'start_time': event.get('DTSTART', ''),
                'end_time': event.get('DTEND', ''),
                'extracted_email': extracted_email,
                'service': {
                    'category': parsed['service']['category'],
                    'type': parsed['service']['type'],
                    'add_ons': parsed['service']['add_ons'],
                    'confidence': parsed['service'].get('match_confidence', 0),
                    'service_id': parsed['service'].get('service_id')
                },
                'raw_summary': parsed['raw']
            }
            sample_appointments.append(sample)
        
        # Count emails in all client appointments
        total_emails_found = 0
        for event in client_appointments:
            if self.extract_email_from_event(event):
                total_emails_found += 1
        
        analysis = {
            'total_events': len(events),
            'client_appointments': len(client_appointments),
            'personal_events': appointment_types.get('personal', 0),
            'other_events': len(events) - len(client_appointments) - appointment_types.get('personal', 0),
            'emails_found': total_emails_found,
            'email_coverage': f"{total_emails_found}/{len(client_appointments)}" if client_appointments else "0/0",
            'date_range': date_range,
            'service_categories': service_categories,
            'customers': sorted(list(customers)),
            'add_ons_used': add_ons_used,
            'sample_appointments': sample_appointments
        }
        
        if business_services:
            analysis['fuzzy_matching_stats'] = fuzzy_matches
        
        return analysis

    def validate_appointments(self, events: List[Dict], business: Business, employee: Employee,
                             create_missing_customers: bool, create_missing_services: bool) -> Dict:
        """
        Validate appointments without saving (dry run mode)
        """
        logger.info("Validating appointments (dry run)")
        
        # Load business services and add-ons
        business_services = list(Service.objects.filter(business=business, is_active=True).select_related('category'))
        business_addons = list(AddOn.objects.filter(business=business, is_active=True))
        
        # Filter client appointments
        client_appointments = []
        for event in events:
            summary = event.get('SUMMARY', '')
            parsed = self.parse_appointment_summary(summary)
            
            if parsed['type'] == 'client_appointment':
                service_info = parsed['service']['raw']
                enhanced_service = self.parse_service_details(service_info, business_services, business_addons)
                parsed['service'] = enhanced_service
                event['parsed'] = parsed
                client_appointments.append(event)
        
        stats = {
            'total_events': len(events),
            'client_appointments': len(client_appointments),
            'created_customers': 0,
            'matched_customers': 0,
            'created_services': 0,
            'matched_services': 0,
            'created_appointments': 0,
            'skipped_appointments': 0,
            'errors': [],
            'warnings': []
        }
        
        for event in client_appointments:
            try:
                parsed = event['parsed']
                customer_name = parsed['customer_name']
                
                # Check if customer exists
                name_parts = customer_name.split(' ')
                first_name = name_parts[0] if name_parts else customer_name
                last_name = ' '.join(name_parts[1:]) if len(name_parts) > 1 else ''
                
                try:
                    business_customer = BusinessCustomer.objects.select_related(
                        'customer__user'
                    ).get(
                        customer__user__first_name__iexact=first_name,
                        customer__user__last_name__iexact=last_name,
                        business=business
                    )
                    stats['matched_customers'] += 1
                except BusinessCustomer.DoesNotExist:
                    if create_missing_customers:
                        stats['created_customers'] += 1
                        stats['warnings'].append(f"Will create new customer: {customer_name}")
                    else:
                        stats['errors'].append(f"Customer not found: {customer_name}")
                        continue
                
                # Check service using fuzzy matching results
                if parsed['service']['service_id']:
                    stats['matched_services'] += 1
                    confidence = parsed['service']['match_confidence']
                    logger.debug(f"Matched service: {parsed['service']['name']} (ID: {parsed['service']['service_id']}, confidence: {confidence:.2f})")
                else:
                    service_category = parsed['service']['category'] or 'Imported'
                    service_type = parsed['service']['type'] or 'Service'
                    service_name = f"{service_category} {service_type}"
                    if parsed['service']['refill_period']:
                        service_name += f" {parsed['service']['refill_period']}"
                    
                    if create_missing_services:
                        stats['created_services'] += 1
                        stats['warnings'].append(f"Will create new service: {service_name}")
                    else:
                        stats['errors'].append(f"Service not found: {service_name} (fuzzy match failed)")
                        continue
                
                stats['created_appointments'] += 1
                
            except Exception as e:
                stats['errors'].append(f"Error validating appointment {parsed.get('customer_name', 'Unknown')}: {str(e)}")
        
        return stats

    def import_appointments(self, events: List[Dict], business: Business, employee: Employee,
                           create_missing_customers: bool, create_missing_services: bool) -> Dict:
        """
        Actually import appointments to Django models
        """
        logger.info("Importing appointments to database")
        
        # Load business services and add-ons
        business_services = list(Service.objects.filter(business=business, is_active=True).select_related('category'))
        business_addons = list(AddOn.objects.filter(business=business, is_active=True))
        
        # Filter client appointments
        client_appointments = []
        for event in events:
            summary = event.get('SUMMARY', '')
            parsed = self.parse_appointment_summary(summary)
            
            if parsed['type'] == 'client_appointment':
                service_info = parsed['service']['raw']
                enhanced_service = self.parse_service_details(service_info, business_services, business_addons)
                parsed['service'] = enhanced_service
                event['parsed'] = parsed
                client_appointments.append(event)
        
        stats = {
            'total_events': len(events),
            'client_appointments': len(client_appointments),
            'created_customers': 0,
            'matched_customers': 0,
            'created_services': 0,
            'matched_services': 0,
            'created_appointments': 0,
            'skipped_appointments': 0,
            'errors': [],
            'warnings': []
        }
        
        for event in client_appointments:
            try:
                parsed = event['parsed']
                customer_name = parsed['customer_name']
                
                # Parse datetime from ICS format (20250709T170000Z)
                start_time_str = event['DTSTART']
                end_time_str = event['DTEND']
                
                # Convert from UTC to timezone-aware datetime
                start_time = datetime.strptime(start_time_str, '%Y%m%dT%H%M%SZ')
                end_time = datetime.strptime(end_time_str, '%Y%m%dT%H%M%SZ')
                start_time = pytz.UTC.localize(start_time)
                end_time = pytz.UTC.localize(end_time)
                
                # Get or create customer
                name_parts = customer_name.split(' ')
                first_name = name_parts[0] if name_parts else customer_name
                last_name = ' '.join(name_parts[1:]) if len(name_parts) > 1 else ''
                
                try:
                    business_customer = BusinessCustomer.objects.select_related(
                        'customer__user'
                    ).get(
                        customer__user__first_name__iexact=first_name,
                        customer__user__last_name__iexact=last_name,
                        business=business
                    )
                    stats['matched_customers'] += 1
                except BusinessCustomer.DoesNotExist:
                    if create_missing_customers:
                        # Try to extract email from ICS event
                        extracted_email = self.extract_email_from_event(event)
                        
                        if extracted_email:
                            # Use actual email from ICS file
                            email = extracted_email
                            # Check if this email already exists
                            if User.objects.filter(email=email).exists():
                                # Email exists, try to match by email instead of name
                                try:
                                    existing_user = User.objects.get(email=email)
                                    existing_customer = CustomerProfile.objects.get(user=existing_user)
                                    business_customer, created = BusinessCustomer.objects.get_or_create(
                                        business=business,
                                        customer=existing_customer,
                                        defaults={'import_source': 'ICS Calendar Import'}
                                    )
                                    if created:
                                        stats['created_customers'] += 1
                                    else:
                                        stats['matched_customers'] += 1
                                    continue
                                except (User.DoesNotExist, CustomerProfile.DoesNotExist):
                                    # Shouldn't happen but handle gracefully
                                    pass
                        else:
                            # No email in ICS, generate placeholder
                            email = f"{first_name.lower()}.{last_name.lower()}@calendar-import.placeholder"
                            # Ensure unique email if duplicate names exist
                            counter = 1
                            while User.objects.filter(email=email).exists():
                                email = f"{first_name.lower()}.{last_name.lower()}.{counter}@calendar-import.placeholder"
                                counter += 1
                        
                        # Generate unique placeholder phone number for imported users
                        import random
                        phone_number = f"+15550000{random.randint(100, 999)}"
                        while User.objects.filter(phone_number=phone_number).exists():
                            phone_number = f"+15550000{random.randint(100, 999)}"
                        
                        user = User.objects.create(
                            email=email,
                            phone_number=phone_number,
                            first_name=first_name,
                            last_name=last_name,
                        )
                        
                        # Create CustomerProfile
                        customer_profile = CustomerProfile.objects.create(
                            user=user
                        )
                        
                        # Create BusinessCustomer relationship
                        import_source = 'ICS Calendar Import (with email)' if extracted_email else 'ICS Calendar Import (no email)'
                        business_customer = BusinessCustomer.objects.create(
                            business=business,
                            customer=customer_profile,
                            import_source=import_source
                        )
                        
                        stats['created_customers'] += 1
                    else:
                        stats['errors'].append(f"Customer not found: {customer_name}")
                        continue
                
                # Get or create service using fuzzy matching results
                if parsed['service']['service_id']:
                    # Use the fuzzy-matched service
                    try:
                        service = Service.objects.get(id=parsed['service']['service_id'], business=business)
                        stats['matched_services'] += 1
                        confidence = parsed['service']['match_confidence']
                        logger.debug(f"Using matched service: {service.name} (ID: {service.id}, confidence: {confidence:.2f})")
                    except Service.DoesNotExist:
                        stats['errors'].append(f"Matched service ID {parsed['service']['service_id']} not found")
                        continue
                else:
                    # Fallback to service creation if fuzzy matching failed
                    service_category = parsed['service']['category'] or 'Imported'
                    service_type = parsed['service']['type'] or 'Service'
                    service_name = f"{service_category} {service_type}"
                    if parsed['service'].get('refill_period'):
                        service_name += f" {parsed['service']['refill_period']}"
                    if create_missing_services:
                        # Get or create service category with fallback for None/empty names
                        category_name = parsed['service']['category'] or 'Imported Services'
                        category, _ = ServiceCategory.objects.get_or_create(
                            name=category_name,
                            business=business
                        )
                        
                        # Calculate actual duration from ICS file for new service
                        actual_duration = end_time - start_time
                        duration_for_service = actual_duration if actual_duration.total_seconds() > 0 else timedelta(minutes=60)
                        
                        # Create service
                        service = Service.objects.create(
                            name=service_name,
                            business=business,
                            category=category,
                            base_duration=duration_for_service,  # Use actual ICS duration
                            base_price=0.00,
                            description=f"Imported from calendar: {parsed['service']['raw']}"
                        )
                        logger.info(f"Created new service '{service_name}' with duration: {int(duration_for_service.total_seconds() // 60)} minutes")
                        stats['created_services'] += 1
                    else:
                        stats['errors'].append(f"Service not found: {service_name} (fuzzy match failed)")
                        continue
                
                # Check for duplicate appointments  
                existing_appointment = Appointment.objects.filter(
                    customer=business_customer,  # Use BusinessCustomer instance
                    employee=employee,
                    start_time=start_time,
                    status__in=['pending', 'confirmed']
                ).first()
                
                if existing_appointment:
                    stats['skipped_appointments'] += 1
                    stats['warnings'].append(f"Skipped duplicate appointment for {customer_name} at {start_time}")
                    continue
                
                # Create appointment and service in a single transaction (CRITICAL FIX)
                with transaction.atomic():
                    appointment = Appointment.objects.create(
                        customer=business_customer,  # Use BusinessCustomer instance
                        employee=employee,
                        start_time=start_time,
                        status='confirmed',
                        # notes=f"Imported from calendar: {parsed['raw']}"
                    )
                    
                    # Create appointment service (REQUIRED - without this, appointments are invisible)
                    # Calculate actual duration from ICS file (end_time - start_time)
                    actual_duration = end_time - start_time
                    duration_minutes = int(actual_duration.total_seconds() // 60)
                    
                    logger.debug(f"Using actual ICS duration: {duration_minutes} minutes (from {start_time} to {end_time})")
                    
                    AppointmentService.objects.create(
                        appointment=appointment,
                        service=service,
                        base_price=service.base_price,
                        duration=duration_minutes
                    )
                    
                    # Create appointment add-ons if any were matched
                    if parsed['service']['add_on_ids']:
                        for addon_id in parsed['service']['add_on_ids']:
                            try:
                                addon = AddOn.objects.get(id=addon_id, business=business)
                                AppointmentAddOn.objects.create(
                                    appointment=appointment,
                                    add_on=addon,  # ✅ Correct field name
                                    add_on_price=addon.base_price,  # ✅ Correct field name
                                    duration=int(addon.base_duration.total_seconds() // 60)
                                )
                                logger.debug(f"Added add-on: {addon.name}")
                            except AddOn.DoesNotExist:
                                logger.warning(f"Add-on ID {addon_id} not found")
                    
                    logger.info(f"Created appointment {appointment.id} with service {service.name}")
                
                stats['created_appointments'] += 1
                
            except Exception as e:
                stats['errors'].append(f"Error importing appointment {parsed.get('customer_name', 'Unknown')}: {str(e)}")
                logger.error(f"Import error: {str(e)}")
        
        return stats

    def process_file(self, ics_file, business_id: int, employee_id: int, 
                    analyze_only: bool = False, dry_run: bool = False,
                    create_missing_customers: bool = True, 
                    create_missing_services: bool = False) -> Dict:
        """
        Main entry point for processing ICS files
        """
        # Validate business and employee
        try:
            business = Business.objects.get(id=business_id)
            employee = Employee.objects.get(id=employee_id, business=business)
        except (Business.DoesNotExist, Employee.DoesNotExist) as e:
            raise ValueError(f"Invalid business or employee: {e}")
        
        # Save file temporarily and parse
        with tempfile.NamedTemporaryFile(mode='wb', suffix='.ics', delete=False) as temp_file:
            for chunk in ics_file.chunks():
                temp_file.write(chunk)
            temp_file_path = temp_file.name
        
        try:
            # Parse events from ICS file
            events = self.parse_ics_file(temp_file_path)
            if not events:
                raise ValueError("No events found in ICS file")
            
            # Load business services and add-ons for analysis
            business_services = list(Service.objects.filter(
                business=business, is_active=True
            ).select_related('category'))
            business_addons = list(AddOn.objects.filter(
                business=business, is_active=True
            ))
            
            if analyze_only:
                # Just analyze and return preview
                analysis = self.analyze_parsed_data(events, business_services, business_addons)
                
                # Get preview of client appointments
                preview = []
                for event in events:
                    summary = event.get('SUMMARY', '')
                    parsed = self.parse_appointment_summary(summary)
                    
                    if parsed['type'] == 'client_appointment':
                        service_info = parsed['service']['raw']
                        enhanced_service = self.parse_service_details(service_info, business_services, business_addons)
                        parsed['service'] = enhanced_service
                        
                        preview_item = {
                            'customer_name': parsed['customer_name'],
                            'start_time': event.get('DTSTART', ''),
                            'end_time': event.get('DTEND', ''),
                            'service': enhanced_service,
                            'raw_summary': parsed['raw']
                        }
                        preview.append(preview_item)
                
                return {
                    'analysis': analysis,
                    'preview': preview[:10]  # First 10 appointments
                }
            
            elif dry_run:
                # Validate without importing
                results = self.validate_appointments(
                    events, business, employee, 
                    create_missing_customers, create_missing_services
                )
                return results
            
            else:
                # Actually import
                results = self.import_appointments(
                    events, business, employee,
                    create_missing_customers, create_missing_services
                )
                return results
                
        finally:
            # Clean up temp file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)


# Singleton instance
calendar_import_service = CalendarImportService()
