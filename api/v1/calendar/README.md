# 📅 Google Calendar Integration API - Testing Guide

## Overview

This document provides a complete walkthrough for testing the Google Calendar OAuth integration flow. The system allows users to connect their Google Calendar accounts to sync appointments from the Django app to their personal calendars.

## 🔧 Prerequisites

### 1. Django Setup
- Django server running on `http://localhost:8000`
- User account created and authenticated
- Calendar integration app installed and migrated

### 2. Google Cloud Console Setup
- Google Cloud Project created
- Calendar API enabled
- OAuth 2.0 credentials configured with:
  - **Authorized redirect URI**: `http://localhost:8000/api/v1/calendar/oauth/google/callback/`

### 3. Environment Configuration
- `settings.py` contains `GOOGLE_OAUTH_CONFIG` with your OAuth credentials
- `DEBUG = True` for development testing

## 🚀 Complete Testing Walkthrough

### Step 1: Authenticate with Django

First, get a valid JWT token by logging into Django:

```bash
# Login and get JWT token
curl -X POST http://localhost:8000/api/v1/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your-password"
  }'

# example
curl -X POST http://localhost:8000/api/v1/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "AdminPass123!"
  }'
```

**Expected Response:**
```json
{
  "access": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "790d46af-5a4a-4643-a562-8cde02807a3e",
    "email": "<EMAIL>",
    "first_name": "Your",
    "last_name": "Name"
  }
}
```

**Save the access token:**
```bash
export JWT_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### Step 2: Check Existing Calendar Connections

Before creating a new connection, check if you already have one:

```bash
curl -X GET http://localhost:8000/api/v1/calendar/connections/ \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**Expected Response (no connections):**
```json
{
  "success": true,
  "connections": [],
  "count": 0
}
```

### Step 3: Initiate Google OAuth Flow

Start the OAuth process to get the authorization URL:

```bash
curl -X POST http://localhost:8000/api/v1/calendar/oauth/google/initiate/ \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**Expected Response:**
```json
{
  "success": true,
  "oauth_url": "https://accounts.google.com/o/oauth2/auth?response_type=code&client_id=************-q37lcl2s6am4bvua7kijb9erqglg42a7.apps.googleusercontent.com&redirect_uri=http%3A%2F%2Flocalhost%3A8000%2Fapi%2Fv1%2Fcalendar%2Foauth%2Fgoogle%2Fcallback%2F&scope=https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcalendar.events%20https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile%20https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email&access_type=offline&prompt=consent&state=XMgc9jrjqPjgLyPAwyW6zfdWUtK4jd6bfzKhGOtOgtE&include_granted_scopes=true",
  "state": "XMgc9jrjqPjgLyPAwyW6zfdWUtK4jd6bfzKhGOtOgtE",
  "provider": "google"
}
```

### Step 4: Complete Google OAuth Authorization

1. **Copy the `oauth_url`** from the response above
2. **Open the URL in your browser**
3. **Sign in to your Google account**
4. **Grant permissions** for:
   - View and edit events on all your calendars
   - See your personal info, including any personal info you've made publicly available
   - See your primary Google Account email address
5. **You'll be redirected** to `http://localhost:8000/api/v1/calendar/oauth/google/callback/` with the authorization result
```bash
# example result in default:
http://localhost:8000/api/v1/calendar/oauth/google/callback/?state=UmBQ02tGKdSfco4lZWuOU1t1WgGMLVcjboqTse13kko&code=4/0AVMBsJh8K_B2Qn70BLrCNqkhFrSdk4uWKIjrNBnT1Y7wU7Tm0ABFjRVWycyHItvONlk-Vg&scope=email%20profile%20https://www.googleapis.com/auth/calendar.events%20https://www.googleapis.com/auth/userinfo.profile%20https://www.googleapis.com/auth/userinfo.email%20openid&authuser=0&hd=nyu.edu&prompt=consent

# add &skip_state=true at the end of the url manually for testing (non-production)
http://localhost:8000/api/v1/calendar/oauth/google/callback/?state=UmBQ02tGKdSfco4lZWuOU1t1WgGMLVcjboqTse13kko&code=4/0AVMBsJh8K_B2Qn70BLrCNqkhFrSdk4uWKIjrNBnT1Y7wU7Tm0ABFjRVWycyHItvONlk-Vg&scope=email%20profile%20https://www.googleapis.com/auth/calendar.events%20https://www.googleapis.com/auth/userinfo.profile%20https://www.googleapis.com/auth/userinfo.email%20openid&authuser=0&hd=nyu.edu&prompt=consent&skip_state=true
```


### Step 5: Verify Successful Connection

After OAuth completion, you should see a JSON response like:

```json
HTTP 200 OK
Allow: GET, HEAD, OPTIONS
Content-Type: application/json
Vary: Accept

{
    "success": true,
    "message": "Google Calendar connected successfully",
    "connection": {
        "id": "abef03a3-b933-4b6b-83c6-ad3c548621cf",
        "provider": "google",
        "provider_display": "Google Calendar",
        "provider_calendar_id": "primary",
        "calendar_name": "Yuhao Sheng - Google Calendar",
        "connection_status": "active",
        "status_display": "Active",
        "is_active": true,
        "needs_reauth": false,
        "sync_enabled": true,
        "last_sync_at": null,
        "last_sync_status": "",
        "last_error": "",
        "error_count": 0,
        "created_at": "2025-07-31T19:03:02-0700",
        "updated_at": "2025-07-31T19:03:02-0700",
        "sync_settings": {
            "auto_sync": true,
            "sync_reminders": true,
            "sync_attendees": true
        },
        "has_access_token": true,
        "has_refresh_token": true,
        "token_expires_at": "2025-07-31T20:03:01.706756-07:00"
    }
}
```

### Step 6: Verify Connection List

Check that your connection appears in the connections list:

```bash
curl -X GET http://localhost:8000/api/v1/calendar/connections/ \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**Expected Response:**
```json
{
  "success": true,
  "connections": [
    {
      "id": "abef03a3-b933-4b6b-83c6-ad3c548621cf",
      "provider": "google",
      "provider_display": "Google Calendar",
      "connection_status": "active",
      "calendar_name": "Your Name - Google Calendar",
      "has_access_token": true,
      "has_refresh_token": true
    }
  ],
  "count": 1
}
```

### Step 7: Check Sync Status

Monitor the sync status of your connections:

```bash
curl -X GET http://localhost:8000/api/v1/calendar/sync/status/ \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**Expected Response:**
```json
{
  "success": true,
  "sync_stats": {
    "total_connections": 1,
    "active_connections": 1,
    "connections_with_errors": 0,
    "last_sync_times": {
      "google": {
        "last_sync_at": null,
        "status": "active",
        "error_count": 0
      }
    }
  }
}
```

## 📋 API Endpoints Reference

### Authentication Required Endpoints

All endpoints except OAuth callback require JWT authentication:
```
Authorization: Bearer <your-jwt-token>
```

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/calendar/connections/` | List user's calendar connections |
| `POST` | `/api/v1/calendar/oauth/google/initiate/` | Start Google OAuth flow |
| `GET` | `/api/v1/calendar/connections/<uuid>/` | Get specific connection details |
| `DELETE` | `/api/v1/calendar/connections/<uuid>/` | Disconnect calendar |
| `GET` | `/api/v1/calendar/sync/status/` | Get sync status and statistics |

### OAuth Callback (No Auth Required)

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/calendar/oauth/google/callback/` | Handle Google OAuth callback |

## 🔍 OAuth Flow Details

### Scopes Requested

The OAuth flow requests these Google API scopes:
- `https://www.googleapis.com/auth/calendar.events` - Create/edit calendar events
- `https://www.googleapis.com/auth/userinfo.profile` - Access user profile info
- `https://www.googleapis.com/auth/userinfo.email` - Access user email

### Security Features

- **State Parameter**: Prevents CSRF attacks during OAuth flow
- **PKCE Support**: Uses secure authorization code exchange
- **Token Refresh**: Automatic refresh token handling for long-term access
- **Session Management**: Secure session handling during OAuth flow

## Troubleshooting

### Common Issues

#### 1. "Token is expired" Error
**Problem**: JWT token has expired
**Solution**: Login again to get a fresh token
```bash
# Get new token
curl -X POST http://localhost:8000/api/v1/auth/login/ -H "Content-Type: application/json" -d '{"email": "your-email", "password": "your-password"}'

# Update environment variable
export JWT_TOKEN="new-token-here"
```

#### 2. "Invalid authentication state" Error
**Problem**: Session state mismatch during OAuth callback
**Solution**: For development testing, you can bypass state validation:
```
# Add &skip_state=true to callback URL manually
http://localhost:8000/api/v1/calendar/oauth/google/callback/?code=...&state=...&skip_state=true
```

#### 3. "redirect_uri_mismatch" Error
**Problem**: Google OAuth redirect URI doesn't match registered URI
**Solution**: Update Google Cloud Console with exact URI:
```
http://localhost:8000/api/v1/calendar/oauth/google/callback/
```

#### 4. "invalid_grant" Error
**Problem**: Authorization code expired or already used
**Solution**: Start fresh OAuth flow (codes are single-use and short-lived)

#### 5. "401 Unauthorized" on Google APIs
**Problem**: Insufficient OAuth scopes
**Solution**: The flow now includes all required scopes automatically

### Debug Mode Features

When `DEBUG = True` in Django settings:
- **Auto-bypasses** state validation in OAuth callback
- **Uses Django settings** for OAuth config instead of AWS Secrets Manager
- **Allows HTTP** (non-HTTPS) redirect URIs for local development
- **Enhanced logging** for debugging OAuth flow

### Logs Location

Monitor Django server console for detailed OAuth flow logs:
```bash
# In your Django terminal, watch for:
[INFO] Using Google OAuth config from Django settings
[WARNING] AUTO-BYPASSING STATE VALIDATION IN DEBUG MODE!
[INFO] Created new calendar connection <NAME_EMAIL>
```

## Related Documentation

- [Google Calendar API Reference](https://developers.google.com/calendar/api/v3/reference)
- [Google OAuth 2.0 Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Django REST Framework Authentication](https://www.django-rest-framework.org/api-guide/authentication/)

## Testing Checklist

- [ ] Django server running
- [ ] User authenticated with valid JWT token
- [ ] Google Cloud Console configured with correct redirect URI
- [ ] OAuth initiation returns valid `oauth_url`
- [ ] Google authorization completes successfully
- [ ] Calendar connection shows as "active"
- [ ] Connection appears in connections list
- [ ] Sync status shows healthy connection

---

✅ **Success Indicator**: When you see `"connection_status": "active"` and `"has_access_token": true` in your connection response, your Google Calendar integration is ready for appointment syncing!