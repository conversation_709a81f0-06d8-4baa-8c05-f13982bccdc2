from rest_framework import serializers
from django.core.validators import FileExtensionValidator
from calendar_integration.models import CalendarConnection, CalendarEvent


class CalendarConnectionSerializer(serializers.ModelSerializer):
    """Serializer for calendar connections"""
    
    provider_display = serializers.CharField(source='get_provider_display', read_only=True)
    status_display = serializers.CharField(source='get_connection_status_display', read_only=True)
    is_active = serializers.BooleanField(read_only=True)
    needs_reauth = serializers.BooleanField(read_only=True)
    
    # OAuth token info (without exposing actual tokens)
    has_access_token = serializers.SerializerMethodField()
    has_refresh_token = serializers.SerializerMethodField()
    token_expires_at = serializers.SerializerMethodField()
    
    class Meta:
        model = CalendarConnection
        fields = [
            'id',
            'provider',
            'provider_display',
            'provider_calendar_id',
            'calendar_name',
            'connection_status',
            'status_display',
            'is_active',
            'needs_reauth',
            'sync_enabled',
            'last_sync_at',
            'last_sync_status',
            'last_error',
            'error_count',
            'created_at',
            'updated_at',
            'sync_settings',
            'has_access_token',
            'has_refresh_token',
            'token_expires_at'
        ]
        read_only_fields = [
            'id',
            'connection_status',
            'last_sync_at',
            'last_sync_status',
            'last_error',
            'error_count',
            'created_at',
            'updated_at'
        ]
    
    def get_has_access_token(self, obj):
        """Check if connection has access token"""
        if obj.social_account:
            return bool(obj.social_account.access_token)
        return False
    
    def get_has_refresh_token(self, obj):
        """Check if connection has refresh token"""
        if obj.social_account:
            return bool(obj.social_account.refresh_token)
        return False
    
    def get_token_expires_at(self, obj):
        """Get token expiry time"""
        if obj.social_account and obj.social_account.token_expiry:
            return obj.social_account.token_expiry.isoformat()
        return None


class CalendarEventSerializer(serializers.ModelSerializer):
    """Serializer for calendar events"""
    
    connection_provider = serializers.CharField(source='calendar_connection.provider', read_only=True)
    sync_status_display = serializers.CharField(source='get_sync_status_display', read_only=True)
    
    class Meta:
        model = CalendarEvent
        fields = [
            'id',
            'appointment_id',
            'external_event_id',
            'external_calendar_id',
            'connection_provider',
            'sync_status',
            'sync_status_display',
            'event_data_hash',
            'last_synced_at',
            'created_at',
            'updated_at'
        ]
        read_only_fields = [
            'id',
            'last_synced_at',
            'created_at',
            'updated_at'
        ]


class CalendarConnectionCreateSerializer(serializers.Serializer):
    """Serializer for creating calendar connections"""
    
    provider = serializers.ChoiceField(choices=CalendarConnection.ProviderChoices.choices)
    calendar_name = serializers.CharField(max_length=255, required=False, allow_blank=True)
    sync_settings = serializers.JSONField(required=False)
    
    def validate_provider(self, value):
        """Validate provider is supported"""
        if value not in dict(CalendarConnection.ProviderChoices.choices):
            raise serializers.ValidationError("Unsupported calendar provider")
        return value


class CalendarSyncStatsSerializer(serializers.Serializer):
    """Serializer for calendar sync statistics"""
    
    total_connections = serializers.IntegerField()
    active_connections = serializers.IntegerField()
    connections_with_errors = serializers.IntegerField()
    last_sync_times = serializers.DictField()
    
    def to_representation(self, instance):
        """Custom representation for sync stats"""
        return {
            'total_connections': instance.get('total_connections', 0),
            'active_connections': instance.get('active_connections', 0),
            'connections_with_errors': instance.get('connections_with_errors', 0),
            'last_sync_times': instance.get('last_sync_times', {}),
            'overall_status': self._get_overall_status(instance)
        }
    
    def _get_overall_status(self, instance):
        """Determine overall sync status"""
        total = instance.get('total_connections', 0)
        active = instance.get('active_connections', 0)
        errors = instance.get('connections_with_errors', 0)
        
        if total == 0:
            return 'no_connections'
        elif errors > 0:
            return 'has_errors'
        elif active == total:
            return 'healthy'
        else:
            return 'partial'


class CalendarImportSerializer(serializers.Serializer):
    """Serializer for calendar import requests"""
    
    ics_file = serializers.FileField(
        validators=[FileExtensionValidator(['ics'])],
        help_text="ICS calendar file to import"
    )
    employee_id = serializers.IntegerField(
        help_text="Employee ID to assign appointments to"
    )
    
    # Mode flags
    analyze_only = serializers.BooleanField(
        default=False,
        help_text="If true, only analyze the file and return preview"
    )
    dry_run = serializers.BooleanField(
        default=False,
        help_text="If true, validate without creating appointments"
    )
    
    # Import options
    create_missing_customers = serializers.BooleanField(
        default=True,
        help_text="Whether to create missing customers"
    )
    create_missing_services = serializers.BooleanField(
        default=False,
        help_text="Whether to create missing services"
    )
    
    def validate(self, data):
        """Validate that analyze_only and dry_run are not both true"""
        if data.get('analyze_only') and data.get('dry_run'):
            raise serializers.ValidationError(
                "analyze_only and dry_run cannot both be true"
            )
        return data


class CalendarImportResponseSerializer(serializers.Serializer):
    """Serializer for calendar import responses"""
    
    mode = serializers.ChoiceField(
        choices=['analysis', 'validation', 'import']
    )
    
    # Analysis mode fields
    analysis = serializers.DictField(required=False)
    preview = serializers.ListField(required=False)
    
    # Validation mode fields
    validation_results = serializers.DictField(required=False)
    
    # Import mode fields
    import_results = serializers.DictField(required=False)
    
    # Common fields
    message = serializers.CharField(required=False)
    warnings = serializers.ListField(
        child=serializers.CharField(),
        required=False
    )
    errors = serializers.ListField(
        child=serializers.CharField(),
        required=False
    )