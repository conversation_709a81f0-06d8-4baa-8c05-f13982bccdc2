from rest_framework import serializers
from reports.models import Transaction
from business.models import BusinessCustomer
from employees.models import Employee
from services.models import Service


class ServiceProviderSerializer(serializers.ModelSerializer):
    """Serializer for service provider (employee) in transactions"""
    name = serializers.SerializerMethodField()
    
    class Meta:
        model = Employee
        fields = ['id', 'name']
    
    def get_name(self, obj):
        return obj.user.get_full_name() or obj.user.email


class CustomerSerializer(serializers.ModelSerializer):
    """Serializer for customer in transactions"""
    name = serializers.SerializerMethodField()
    
    class Meta:
        model = BusinessCustomer
        fields = ['id', 'name']
    
    def get_name(self, obj):
        user = obj.customer.user
        return user.get_full_name() or user.email


class ServiceSerializer(serializers.ModelSerializer):
    """Serializer for service in transactions"""
    
    class Meta:
        model = Service
        fields = ['id', 'name']


class TransactionSerializer(serializers.ModelSerializer):
    """Main serializer for Transaction model"""
    customer_name = serializers.SerializerMethodField()
    service_name = serializers.SerializerMethodField()
    sold_by_name = serializers.SerializerMethodField()
    checkout_by_name = serializers.SerializerMethodField()
    subtotal = serializers.SerializerMethodField()
    total_before_tip = serializers.SerializerMethodField()
    appointment_date = serializers.SerializerMethodField()
    
    # Nested objects for filtering
    customer = CustomerSerializer(read_only=True)
    service = ServiceSerializer(read_only=True)
    sold_by = ServiceProviderSerializer(read_only=True)
    checkout_by = ServiceProviderSerializer(read_only=True)
    
    class Meta:
        model = Transaction
        fields = [
            'id',
            'customer',
            'customer_name',
            'checkout_date',
            'appointment_date',
            'checkout_by',
            'checkout_by_name',
            'item_sold',
            'quantity',
            'service',
            'service_name',
            'sold_by',
            'sold_by_name',
            'payment_method',
            'price',
            'tax',
            'tip',
            'discount',
            'amount_paid',
            'subtotal',
            'total_before_tip',
            'created_at',
            'updated_at',
        ]
    
    def get_customer_name(self, obj):
        """Get the customer's full name or email"""
        user = obj.customer.customer.user
        return user.get_full_name() or user.email

    def get_service_name(self, obj):
        """Get the service name"""
        return obj.service.name

    def get_sold_by_name(self, obj):
        return obj.sold_by.user.get_full_name() or obj.sold_by.user.email

    def get_checkout_by_name(self, obj):
        return obj.checkout_by.user.get_full_name() or obj.checkout_by.user.email

    def get_subtotal(self, obj):
        """Calculate subtotal (price * quantity - discount)"""
        return (obj.price * obj.quantity) - obj.discount

    def get_total_before_tip(self, obj):
        """Calculate total before tip (subtotal + tax)"""
        subtotal = (obj.price * obj.quantity) - obj.discount
        return subtotal + obj.tax

    def get_appointment_date(self, obj):
        """Get the appointment date if transaction is from an appointment, None for direct sales"""
        return obj.appointment_date


class TransactionSummarySerializer(serializers.Serializer):
    """Serializer for transaction summary statistics"""
    money_earned = serializers.DecimalField(max_digits=12, decimal_places=2)
    drawer_balance = serializers.DecimalField(max_digits=12, decimal_places=2)
    total_transactions = serializers.IntegerField()
    total_tips = serializers.DecimalField(max_digits=12, decimal_places=2)
    average_transaction = serializers.DecimalField(max_digits=10, decimal_places=2)
    
    # Payment method breakdown
    cash_transactions = serializers.IntegerField()
    card_transactions = serializers.IntegerField()
    digital_transactions = serializers.IntegerField()
    
    # Top performers
    top_employee = serializers.CharField(allow_null=True)
    top_employee_sales = serializers.DecimalField(max_digits=12, decimal_places=2, allow_null=True)


class SalesSummaryBreakdownSerializer(serializers.Serializer):
    """Serializer for sales breakdown by type with business costs"""
    service_sales_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    service_sales_cost = serializers.DecimalField(max_digits=12, decimal_places=2)
    service_sales_count = serializers.IntegerField()
    
    addon_sales_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    addon_sales_cost = serializers.DecimalField(max_digits=12, decimal_places=2)
    addon_sales_count = serializers.IntegerField()


class SalesSummaryAggregatesSerializer(serializers.Serializer):
    """Serializer for aggregate sales data"""
    sales_tax = serializers.DecimalField(max_digits=12, decimal_places=2)
    tips = serializers.DecimalField(max_digits=12, decimal_places=2)
    service_refunds = serializers.DecimalField(max_digits=12, decimal_places=2)
    tip_refunds = serializers.DecimalField(max_digits=12, decimal_places=2)


class SalesSummaryTotalsSerializer(serializers.Serializer):
    """Serializer for sales summary totals"""
    revenue_total = serializers.DecimalField(max_digits=12, decimal_places=2)
    business_cost_total = serializers.DecimalField(max_digits=12, decimal_places=2)
    tax_total = serializers.DecimalField(max_digits=12, decimal_places=2)
    profit_total = serializers.DecimalField(max_digits=12, decimal_places=2)


class SalesSummarySerializer(serializers.Serializer):
    """Complete sales summary serializer"""
    breakdown = SalesSummaryBreakdownSerializer()
    aggregates = SalesSummaryAggregatesSerializer()
    totals = SalesSummaryTotalsSerializer()
    
    # Period information
    period_start = serializers.DateField()
    period_end = serializers.DateField()
    total_transactions = serializers.IntegerField()


class FilterOptionsSerializer(serializers.Serializer):
    """Serializer for filter dropdown options"""
    service_providers = ServiceProviderSerializer(many=True)
    customers = CustomerSerializer(many=True) 