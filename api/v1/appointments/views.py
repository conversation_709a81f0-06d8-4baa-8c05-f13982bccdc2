from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from datetime import datetime, timedelta, time
import logging
from django.utils import timezone
from utils.timezone_utils import (
    get_business_timezone,
    localize_time_to_date,
    format_time_for_display,
    now_in_timezone,
    convert_to_business_timezone,
    calculate_employee_availability
)

from appointments.models import Appointment, AppointmentHistory
from services.models import EmployeeService
from employees.models import EmployeeWorkingHours
from business.models import OnlineBookingRules
from ..services.serializers import AppointmentSerializer, DetailedAppointmentSerializer, AppointmentHistorySerializer
from api.permissions import PublicReadOnlyPrivateWrite

logger = logging.getLogger(__name__)

class AppointmentViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows appointments to be viewed or created.
    Also provides a method to get available time slots.
    """
    queryset = Appointment.objects.all()
    serializer_class = AppointmentSerializer
    permission_classes = [PublicReadOnlyPrivateWrite]
    
    def get_queryset(self):
        """
        Filter appointments by business_id, customer_id, employee_id, and date range if provided in query parameters
        """
        queryset = super().get_queryset()
        
        # OPTIMIZATION: Prevent N+1 queries by prefetching related data
        queryset = queryset.select_related(
            'customer',
            'customer__customer',
            'customer__customer__user',
            'customer__business',
            'employee',
            'employee__user',
            'employee__business'
        ).prefetch_related(
            'appointment_services',
            'appointment_services__service',
            'appointment_services__service__category',
            'appointment_add_ons',
            'appointment_add_ons__add_on'
        )

        # Filter by business_id if provided
        business_id = self.request.query_params.get('business_id')
        if business_id:
            queryset = queryset.filter(employee__business_id=business_id)

        # Filter by customer_id if provided
        customer_id = self.request.query_params.get('customer_id')
        if customer_id:
            queryset = queryset.filter(customer_id=customer_id)

        # FIX: Add employee filtering support (for iOS cache performance)
        employee_ids = self.request.query_params.get('employee__in')
        if employee_ids:
            try:
                # Parse comma-separated employee IDs
                employee_id_list = [int(id.strip()) for id in employee_ids.split(',') if id.strip().isdigit()]
                if employee_id_list:
                    queryset = queryset.filter(employee_id__in=employee_id_list)
            except (ValueError, AttributeError):
                # Invalid employee IDs, ignore the filter
                pass

        # FIX: Add date range filtering support (for iOS cache performance)
        # Support both 'from/to' format (like EmployeeAppointmentsView) and 'start_time__gte/lt' format
        from_date_str = self.request.query_params.get('from') or self.request.query_params.get('start_time__gte')
        to_date_str = self.request.query_params.get('to') or self.request.query_params.get('start_time__lt')
        
        if from_date_str:
            try:
                from datetime import datetime
                # Parse date (support both date and datetime formats)
                if 'T' in from_date_str:
                    # Full datetime format: 2024-01-01T00:00:00
                    from_datetime = datetime.fromisoformat(from_date_str.replace('Z', '+00:00'))
                else:
                    # Date only format: 2024-01-01
                    from_date = datetime.strptime(from_date_str, '%Y-%m-%d').date()
                    from_datetime = datetime.combine(from_date, datetime.min.time())
                
                queryset = queryset.filter(start_time__gte=from_datetime)
            except (ValueError, TypeError):
                # Invalid date format, ignore the filter
                pass
        
        if to_date_str:
            try:
                from datetime import datetime
                # Parse date (support both date and datetime formats)
                if 'T' in to_date_str:
                    # Full datetime format: 2024-01-01T23:59:59
                    to_datetime = datetime.fromisoformat(to_date_str.replace('Z', '+00:00'))
                else:
                    # Date only format: 2024-01-01
                    to_date = datetime.strptime(to_date_str, '%Y-%m-%d').date()
                    to_datetime = datetime.combine(to_date, datetime.max.time())
                
                queryset = queryset.filter(start_time__lt=to_datetime)
            except (ValueError, TypeError):
                # Invalid date format, ignore the filter
                pass

        return queryset
    
    def get_serializer_class(self):
        """
        Use different serializers based on the action
        """
        if self.action == 'retrieve':
            return DetailedAppointmentSerializer
        return AppointmentSerializer
    
    def create(self, request, *args, **kwargs):
        """
        Create a new appointment
        """
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def _calculate_employee_availability(self, employee, booking_date, service, business_timezone, 
                                        existing_appointments, interval_minutes, default_start_time, 
                                        default_end_time, day_of_week, today_business, now_business, booking_rules=None):
        """
        Core helper method to calculate availability slots for a specific employee
        
        Returns a list of availability slots
        """
        # Get employee's full name for the dictionary key
        employee_name = f"{employee.user.first_name} {employee.user.last_name}".strip()
        if not employee_name:
            employee_name = f"Employee #{employee.id}"
        
        # Initialize slots for this employee
        available_slots = []
        
        # Try to get working hours for this employee and day
        try:
            working_hours = EmployeeWorkingHours.objects.get(
                employee=employee,
                day=day_of_week,
                is_active=True
            )
            
            start_time = working_hours.start_time
            end_time = working_hours.end_time
            
        except EmployeeWorkingHours.DoesNotExist:
            # Default working hours if not set
            start_time = default_start_time
            end_time = default_end_time
        
        # Get all existing appointments for this employee on this date
        employee_appointments = existing_appointments.filter(employee=employee)
        
        # Create timezone-aware datetime objects using utility function
        current_time = localize_time_to_date(start_time, booking_date, business_timezone)
        end_datetime = localize_time_to_date(end_time, booking_date, business_timezone)
        
        # For today, don't show past times
        if booking_date == today_business:
            # Get the business's booking lead time setting from booking rules
            try:
                if booking_rules:
                    # Convert hours to minutes for lead time
                    lead_time_minutes = booking_rules.min_hours_before * 60
                else:
                    # Default to 60 minutes if no rules provided
                    lead_time_minutes = 60
            except (AttributeError):
                # Default to 60 minutes if setting doesn't exist
                lead_time_minutes = 60
            
            # Apply lead time to current business time
            min_bookable_time = now_business + timedelta(minutes=lead_time_minutes)
            
            # If the minimum bookable time is after start time, adjust start time
            if min_bookable_time.time() > start_time and min_bookable_time.date() == today_business:
                # Round up to the next interval
                minutes_to_add = interval_minutes - (min_bookable_time.minute % interval_minutes)
                if minutes_to_add < interval_minutes:
                    min_bookable_time += timedelta(minutes=minutes_to_add)
                current_time = min_bookable_time.replace(second=0, microsecond=0)
        
        # Calculate actual service duration for this employee
        actual_duration = service.base_duration
        try:
            # Check if employee has custom duration for this service
            employee_service = EmployeeService.objects.get(
                employee=employee, 
                service=service,
                is_active=True
            )
            if employee_service.custom_duration:
                actual_duration = employee_service.custom_duration
        except EmployeeService.DoesNotExist:
            # Use standard duration if no custom duration is set
            pass
        
        # Add buffer time to the service duration
        total_duration = actual_duration + service.buffer_time
        
        # Generate time slots
        while current_time + total_duration <= end_datetime:
            # Format time as HH:MM in business timezone
            time_str = format_time_for_display(current_time, business_timezone)
            
            # Check if this slot is available (not conflicting with existing appointments)
            is_available = True
            
            # Service end time
            service_end_time = current_time + actual_duration
            
            # Check for conflicts with existing appointments
            for appt in employee_appointments:
                # Convert appointment times to business timezone for accurate comparison
                appt_start = convert_to_business_timezone(appt.start_time, business_timezone)
                appt_end = convert_to_business_timezone(appt.end_time, business_timezone)
                
                # If our service overlaps with this appointment
                if (current_time < appt_end and service_end_time > appt_start):
                    is_available = False
                    break
            
            # Add time slot to results with availability status
            available_slots.append({
                'time': time_str,
                'available': is_available
            })
            
            # Move to next time slot based on interval
            current_time += timedelta(minutes=interval_minutes)
        
        # Sort slots by time
        available_slots.sort(key=lambda slot: slot['time'])
        
        # If we didn't find any slots, generate default slots marked as unavailable
        if not available_slots:
            logger.warning(f"No available slots found for employee {employee_name} on date {booking_date}, service {service.id}")
            
            # Generate default slots but mark as unavailable
            current_time = localize_time_to_date(default_start_time, booking_date, business_timezone)
            end_datetime = localize_time_to_date(default_end_time, booking_date, business_timezone)
            
            while current_time < end_datetime:
                time_str = current_time.strftime('%H:%M')
                available_slots.append({
                    'time': time_str,
                    'available': False
                })
                current_time += timedelta(minutes=interval_minutes)
        
        return employee_name, available_slots

    @action(detail=True, methods=['get'], url_path='history')
    def history(self, request, pk=None):
        """
        Get the history of changes for a specific appointment.

        Returns a list of all changes made to the appointment, including:
        - Who made the change (employee, customer, or system)
        - When the change was made
        - What was changed
        - Before and after values
        """
        appointment = self.get_object()
        history_entries = AppointmentHistory.objects.filter(appointment=appointment).order_by('-created_at')

        # Add pagination for large history lists
        page = self.paginate_queryset(history_entries)
        if page is not None:
            serializer = AppointmentHistorySerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = AppointmentHistorySerializer(history_entries, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'], url_path='available-times')
    def available_times(self, request):
        """
        Return available time slots for a given date and service.

        This method is used internally by the BusinessAppointmentViewSet.
        For external API calls, use /api/v1/businesses/{business_id}/appointments/available-times/
        """
        # Handle both DRF Request objects and Django WSGIRequest objects
        if hasattr(request, 'query_params'):
            date_str = request.query_params.get('date')
            service_id = request.query_params.get('service_id')
            employee_id = request.query_params.get('employee_id')
            business_id = request.query_params.get('business_id')
            group_by = request.query_params.get('group_by')
            rule_id_str = request.query_params.get('rule_id')
        else:
            date_str = request.GET.get('date')
            service_id = request.GET.get('service_id')
            employee_id = request.GET.get('employee_id')
            business_id = request.GET.get('business_id')
            group_by = request.GET.get('group_by')
            rule_id_str = request.GET.get('rule_id')

        if not date_str or not service_id:
            return Response(
                {"error": "Date and service_id are required parameters"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            from business.models import Business
            from services.models import Service
            from employees.models import Employee
            from utils.smart_booking_rules import apply_smart_booking_rules

            # Parse the date
            booking_date = datetime.strptime(date_str, '%Y-%m-%d').date()

            # Get service to determine duration
            service = get_object_or_404(Service, id=service_id)
            service_duration = service.base_duration

            # Get the business (either from business_id or from service)
            if business_id:
                business = get_object_or_404(Business, id=business_id)
                # Check if service belongs to this business
                if service.business.id != business.id:
                    logger.warning(f"Service ID {service_id} belongs to business ID {service.business.id}, not business ID {business_id}")
            else:
                business = service.business

            # Get the business's booking settings from OnlineBookingRules
            try:
                booking_rules = OnlineBookingRules.objects.get(business=business)
                # Get the appointment interval from booking rules
                interval_minutes = booking_rules.appointment_interval
            except OnlineBookingRules.DoesNotExist:
                # Default to 15-minute interval if no settings exist
                interval_minutes = 15
                booking_rules = None
                logger.warning(f"No booking rules found for business ID {business.id}, defaulting to UTC and 15-minute interval")

            # Get the business timezone using utility function
            business_timezone = get_business_timezone(business)

            # Get current date and time in business timezone
            now_business = now_in_timezone(business_timezone)
            today_business = now_business.date()

            # Validate that the date is not in the past
            if booking_date < today_business:
                return Response(
                    {"error": "Please select a future date. Past dates are not available for booking."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Find relevant employees
            if employee_id:
                # Filter by specific employee
                employees = Employee.objects.filter(
                    id=employee_id,
                    is_active=True,
                    business=business
                )
                logger.info(f"Filtering by employee ID {employee_id}, found {employees.count()} employees")

                if not employees.exists():
                    return Response(
                        {"error": "Employee not found or not active"},
                        status=status.HTTP_404_NOT_FOUND
                    )
            else:
                # Get all active employees for this business if business_id is specified or group_by=employee
                if business_id or group_by == 'employee':
                    employees = Employee.objects.filter(
                        is_active=True,
                        business=business
                    )
                    logger.info(f"Filtering by business ID {business.id}, found {employees.count()} active employees")
                else:
                    # Otherwise, get employees for this business who offer this service
                    service_employees = EmployeeService.objects.filter(
                        service=service,
                        is_active=True,
                        employee__business=business
                    )
                    logger.info(f"Found {service_employees.count()} employee-service relationships for service ID {service_id} in business ID {business.id}")

                    employee_ids = service_employees.values_list('employee_id', flat=True)
                    employees = Employee.objects.filter(
                        id__in=employee_ids,
                        is_active=True,
                        business=business
                    )
                    logger.info(f"Found {employees.count()} active employees for service ID {service_id} in business ID {business.id}")

                # If no employees are found, log a warning and return empty response
                if not employees.exists():
                    logger.warning(f"No active employees found for business ID {business.id}")

            # Initialize employee availability dictionary
            employee_availability = {}

            # Use interval directly from booking rules without adjustments
            logger.info(f"Using appointment interval of {interval_minutes} minutes for business ID {business.id}")

            # Business hour defaults if no employees are available
            default_start_time = time(9, 0)  # 9:00 AM
            default_end_time = time(17, 0)   # 5:00 PM

            # Get working hours for this day of week
            day_of_week = booking_date.strftime('%A').lower()

            # Get all appointments for this date
            existing_appointments = Appointment.objects.filter(
                start_time__date=booking_date,
                status__in=['pending', 'confirmed', 'in_progress']
            )

            # Get minimum hours before booking from rules
            min_hours_before = booking_rules.min_hours_before if booking_rules else 24

            # Generate time slots using the new simplified availability calculation
            for employee in employees:
                employee_name = f"{employee.user.first_name} {employee.user.last_name}".strip()
                if not employee_name:
                    employee_name = f"Employee #{employee.id}"

                # Use the new bulletproof availability calculation
                available_slots = calculate_employee_availability(
                    employee=employee,
                    target_date=booking_date,
                    service=service,
                    existing_appointments=existing_appointments,
                    interval_minutes=interval_minutes,
                    min_hours_before=min_hours_before
                )

                # Add this employee's slots to the result dictionary
                employee_availability[employee_name] = available_slots

            # Parse and apply smart booking rules if specified
            smart_result = None
            if rule_id_str and rule_id_str != '0':
                try:
                    # Parse rule IDs (support comma-separated values)
                    rule_ids = [int(rid.strip()) for rid in rule_id_str.split(',') if rid.strip().isdigit()]

                    if rule_ids:
                        # Get enabled smart booking rules for this business
                        enabled_rules = []
                        if booking_rules:
                            enabled_rules = booking_rules.get_enabled_smart_rules()

                        # Filter to only requested rules that are enabled
                        enabled_rule_ids = [rule['rule_type'] for rule in enabled_rules if rule['rule_type'] in rule_ids]

                        if enabled_rule_ids:
                            # Apply smart booking rules
                            smart_result = apply_smart_booking_rules(
                                availability_data=employee_availability,
                                rule_ids=enabled_rule_ids,
                                smart_rules=enabled_rules,
                                existing_appointments=existing_appointments,
                                service_duration=service.base_duration.total_seconds() / 60,  # Convert to minutes
                                service=service
                            )
                            logger.info(f"Applied smart booking rules {enabled_rule_ids} for business {business.id}")
                        else:
                            logger.warning(f"Requested rule IDs {rule_ids} are not enabled for business {business.id}")

                except (ValueError, TypeError) as e:
                    logger.warning(f"Invalid rule_id parameter: {rule_id_str}, error: {e}")

            # If no employees are available, return an empty dictionary or array based on group_by
            if not employee_availability:
                logger.warning(f"No available employees found for date {date_str}, service {service_id}, business {business.id}")
                if group_by == 'employee':
                    # For group_by=employee with no available employees, return empty slots for each employee
                    if employees.exists():
                        for employee in employees:
                            employee_name = f"{employee.user.first_name} {employee.user.last_name}".strip()
                            if not employee_name:
                                employee_name = f"Employee #{employee.id}"

                            # No available slots for this employee
                            employee_availability[employee_name] = []

                        # Return with smart rules format if requested
                        if rule_id_str and rule_id_str != '0':
                            return Response({
                                'default': employee_availability,
                                'smart': None
                            })
                        return Response(employee_availability)

                # Return with smart rules format if requested
                empty_result = {} if group_by == 'employee' else []
                if rule_id_str and rule_id_str != '0':
                    return Response({
                        'default': empty_result,
                        'smart': None
                    })
                return Response(empty_result)

            # Prepare the default availability data based on parameters and backward compatibility
            default_availability = None

            # Case 1: Specific employee requested - return just the time slots array for backward compatibility
            if employee_id:
                # Always return dictionary format with employee name as key
                default_availability = employee_availability

            # Case 2: Group by employee explicitly requested - return dictionary with employee names as keys
            elif group_by == 'employee':
                default_availability = employee_availability

            # Case 3: Multiple employees without grouping - merge all slots (backward compatibility)
            else:
                # Always use dictionary format, but merge employee slots if not grouped
                all_employee_availability = {}

                # Merge all slots from all employees (slots are now ISO timestamp strings)
                all_slots_set = set()
                for employee_name, employee_slots in employee_availability.items():
                    for slot in employee_slots:
                        # Add unique time slots (slots are ISO timestamp strings)
                        all_slots_set.add(slot)

                # Convert to sorted list
                all_slots = sorted(list(all_slots_set))

                # Add to dictionary under "All Employees" key
                all_employee_availability["All Employees"] = all_slots
                default_availability = all_employee_availability

            # Return response with smart booking rules if applied
            if smart_result and smart_result.get('smart'):
                # Format smart result based on the same logic as default
                smart_availability = smart_result['smart']['availability']

                # Apply the same formatting logic to smart results
                if employee_id or group_by == 'employee':
                    formatted_smart = smart_availability
                else:
                    # Merge smart slots for "All Employees" format (slots are ISO timestamp strings)
                    all_smart_slots_set = set()
                    for employee_name, employee_slots in smart_availability.items():
                        for slot in employee_slots:
                            # Add unique time slots (slots are ISO timestamp strings)
                            all_smart_slots_set.add(slot)

                    # Convert to sorted list
                    all_smart_slots = sorted(list(all_smart_slots_set))
                    formatted_smart = {"All Employees": all_smart_slots}

                return Response({
                    'default': default_availability,
                    'smart': {
                        'applied_rules': smart_result['smart']['applied_rules'],
                        'availability': formatted_smart
                    }
                })
            else:
                # No smart rules applied, return default format for backward compatibility
                return Response(default_availability)

        except ValueError:
            return Response(
                {"error": "Invalid date format. Use YYYY-MM-DD"},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.exception(f"Error generating available slots: {str(e)}")
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class BusinessAppointmentViewSet(viewsets.ViewSet):
    """
    Business-specific appointment endpoints with automatic smart booking rules application.
    """
    permission_classes = [PublicReadOnlyPrivateWrite]

    @action(detail=False, methods=['get'], url_path='available-times')
    def available_times(self, request, business_id=None):
        """
        Return available time slots for a given business, date, and service with automatic smart booking rules.

        Required path parameters:
        - business_id: ID of the business

        Required query parameters:
        - date: YYYY-MM-DD format
        - service_id: ID of the requested service

        Optional query parameters:
        - employee_id: ID of a specific employee (if not provided, checks all employees)

        Returns:
        A simplified response with applied rules and availability grouped by employee
        """
        from business.models import Business, OnlineBookingRules
        from services.models import Service
        from utils.smart_booking_rules import apply_smart_booking_rules

        # Validate business_id
        if not business_id:
            return Response(
                {"error": "Business ID is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            business = get_object_or_404(Business, id=business_id)
        except Business.DoesNotExist:
            return Response(
                {"error": f"Business with ID {business_id} not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Handle both DRF Request objects and Django WSGIRequest objects
        if hasattr(request, 'query_params'):
            date_str = request.query_params.get('date')
            service_id = request.query_params.get('service_id')
            employee_id = request.query_params.get('employee_id')
        else:
            date_str = request.GET.get('date')
            service_id = request.GET.get('service_id')
            employee_id = request.GET.get('employee_id')

        if not date_str or not service_id:
            return Response(
                {"error": "Date and service_id are required parameters"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Get business booking rules and automatically determine enabled smart rules
            booking_rules = None
            enabled_smart_rules = []
            try:
                booking_rules = OnlineBookingRules.objects.get(business=business)
                enabled_smart_rules = booking_rules.get_enabled_smart_rules()
            except OnlineBookingRules.DoesNotExist:
                logger.warning(f"No booking rules found for business {business_id}")

            # Use the internal availability calculation logic with automatic smart rules
            appointment_viewset = AppointmentViewSet()

            # Create a mock request with the required parameters
            class MockRequest:
                def __init__(self, original_request):
                    self.query_params = {
                        'date': date_str,
                        'service_id': service_id,
                        'business_id': str(business_id),
                        'group_by': 'employee'  # Always group by employee for business endpoints
                    }
                    if employee_id:
                        self.query_params['employee_id'] = employee_id

                    # Add rule_id parameter if smart rules are enabled
                    if enabled_smart_rules:
                        rule_ids = [str(rule['rule_type']) for rule in enabled_smart_rules]
                        self.query_params['rule_id'] = ','.join(rule_ids)
                    else:
                        self.query_params['rule_id'] = '0'  # No smart rules

                    # Copy other attributes from original request
                    self.user = getattr(original_request, 'user', None)
                    self.method = getattr(original_request, 'method', 'GET')
                    self.META = getattr(original_request, 'META', {})

            mock_request = MockRequest(request)

            # Call the existing available_times method
            original_response = appointment_viewset.available_times(mock_request)

            # Transform the response to the simplified format
            if original_response.status_code == 200:
                original_data = original_response.data

                # Extract applied rules and availability from the complex response
                applied_rules = []
                availability = {}

                if isinstance(original_data, dict):
                    if 'smart' in original_data and original_data['smart']:
                        # Smart rules were applied
                        applied_rules = original_data['smart'].get('applied_rules', [])
                        availability = original_data['smart'].get('availability', {})
                    elif 'default' in original_data:
                        # Only default availability (no smart rules)
                        availability = original_data['default']
                    else:
                        # Direct availability format
                        availability = original_data

                # Create simplified response
                simplified_response = {
                    "applied_rules": applied_rules,
                    "availability": availability
                }

                # Log the automatic smart rules application
                if enabled_smart_rules:
                    rule_names = [rule['rule_name'] for rule in enabled_smart_rules]
                    logger.info(f"Automatically applied smart booking rules for business {business_id}: {rule_names}")

                return Response(simplified_response)
            else:
                # Return the original error response
                return original_response

        except ValueError:
            return Response(
                {"error": "Invalid date format. Use YYYY-MM-DD"},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.exception(f"Error generating available slots for business {business_id}: {str(e)}")
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'], url_path='import-calendar')
    def import_calendar(self, request):
        """
        Import appointments from iCalendar (.ics) file
        
        POST /api/v1/appointments/import-calendar/
        
        Required parameters:
        - file: .ics file upload
        - business_id: target business ID
        - default_employee_id: employee to assign appointments to
        
        Optional parameters:
        - dry_run: true/false (validate only, don't save) - default: false
        - skip_duplicates: true/false - default: true
        - create_missing_customers: true/false - default: true
        - create_missing_services: true/false - default: false
        """
        try:
            from django.db import transaction
            from django.core.files.storage import default_storage
            from django.core.files.base import ContentFile
            from business.models import Business
            from employees.models import Employee
            from customers.models import Customer
            from services.models import Service, ServiceCategory
            from appointments.models import Appointment, AppointmentService, AppointmentAddOn
            from services.models import AddOn
            import tempfile
            import os
            from datetime import datetime
            import uuid
            
            # Validate request
            if 'file' not in request.FILES:
                return Response(
                    {"error": "No .ics file provided"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            business_id = request.data.get('business_id')
            default_employee_id = request.data.get('default_employee_id')
            
            if not business_id or not default_employee_id:
                return Response(
                    {"error": "business_id and default_employee_id are required"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Parse options
            dry_run = request.data.get('dry_run', 'false').lower() == 'true'
            skip_duplicates = request.data.get('skip_duplicates', 'true').lower() == 'true'
            create_missing_customers = request.data.get('create_missing_customers', 'true').lower() == 'true'
            create_missing_services = request.data.get('create_missing_services', 'false').lower() == 'true'
            
            # Validate business and employee
            try:
                business = Business.objects.get(id=business_id)
                default_employee = Employee.objects.get(id=default_employee_id, business=business)
            except (Business.DoesNotExist, Employee.DoesNotExist):
                return Response(
                    {"error": "Invalid business_id or default_employee_id"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Save uploaded file temporarily
            uploaded_file = request.FILES['file']
            if not uploaded_file.name.endswith('.ics'):
                return Response(
                    {"error": "File must be a .ics calendar file"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Create temporary file
            with tempfile.NamedTemporaryFile(mode='w+', suffix='.ics', delete=False) as temp_file:
                for chunk in uploaded_file.chunks():
                    temp_file.write(chunk.decode('utf-8'))
                temp_file_path = temp_file.name
            
            try:
                # Parse the ICS file using our parsing logic
                events = self._parse_ics_file(temp_file_path)
                if not events:
                    return Response(
                        {"error": "No events found in the .ics file"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                # Parse and filter client appointments
                client_appointments = []
                for event in events:
                    summary = event.get('SUMMARY', '')
                    parsed = self._parse_appointment_summary(summary)
                    
                    if parsed['type'] == 'client_appointment':
                        # Add timing and parsed data
                        event['parsed'] = parsed
                        client_appointments.append(event)
                
                if not client_appointments:
                    return Response(
                        {"error": "No client appointments found in the .ics file"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                # Import statistics
                stats = {
                    'total_events': len(events),
                    'client_appointments': len(client_appointments),
                    'created_customers': 0,
                    'matched_customers': 0,
                    'created_services': 0,
                    'matched_services': 0,
                    'created_appointments': 0,
                    'skipped_appointments': 0,
                    'errors': [],
                    'warnings': []
                }
                
                # Process appointments in transaction
                if dry_run:
                    # Validate only, don't save
                    result = self._validate_appointments(
                        client_appointments, business, default_employee, 
                        create_missing_customers, create_missing_services, stats
                    )
                else:
                    # Actually import
                    with transaction.atomic():
                        result = self._import_appointments(
                            client_appointments, business, default_employee,
                            skip_duplicates, create_missing_customers, 
                            create_missing_services, stats
                        )
                
                return Response({
                    'status': 'success' if not stats['errors'] else 'partial_success',
                    'dry_run': dry_run,
                    'summary': stats,
                    'message': f"{'Validated' if dry_run else 'Imported'} {stats['created_appointments']} appointments"
                })
                
            finally:
                # Clean up temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                
        except Exception as e:
            logger.exception(f"Error importing calendar: {str(e)}")
            return Response(
                {"error": f"Import failed: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _parse_ics_file(self, file_path):
        """
        Parse ICS file and return list of events
        """
        from collections import defaultdict
        
        events = []
        current_event = {}
        in_event = False
        
        with open(file_path, 'r', encoding='utf-8') as file:
            for line in file:
                line = line.strip()
                
                if line == 'BEGIN:VEVENT':
                    in_event = True
                    current_event = {}
                    continue
                elif line == 'END:VEVENT':
                    in_event = False
                    if current_event:
                        events.append(current_event.copy())
                    current_event = {}
                    continue
                
                if in_event and ':' in line:
                    key, value = line.split(':', 1)
                    current_event[key] = value
        
        return events

    def _parse_appointment_summary(self, summary):
        """
        Parse appointment summary into structured data
        """
        if not summary or summary.strip() == '':
            return {'type': 'unknown', 'raw': summary}
        
        summary = summary.strip()
        
        # Handle empty or placeholder events
        if summary in ['---', '']:
            return {'type': 'placeholder', 'raw': summary}
        
        # Handle personal events
        personal_events = ['Workout', 'Badminton', 'Badminton lesson', 'Lunch', 'Dental', 'Vision', 
                          'Airport pick up', 'Pick up kids']
        if summary in personal_events:
            return {'type': 'personal', 'activity': summary, 'raw': summary}
        
        # Handle staff transitions
        if '>' in summary and len(summary.split('>')) == 2:
            from_staff, to_staff = summary.split('>')
            return {
                'type': 'staff_transition',
                'from_staff': from_staff.strip(),
                'to_staff': to_staff.strip(),
                'raw': summary
            }
        
        # Handle client appointments (Customer Name - Service Type)
        if ' - ' in summary:
            parts = summary.split(' - ', 1)
            if len(parts) == 2:
                customer_name = parts[0].strip()
                service_info = parts[1].strip()
                
                # Parse service details
                parsed_service = self._parse_service_details(service_info)
                
                return {
                    'type': 'client_appointment',
                    'customer_name': customer_name,
                    'service': parsed_service,
                    'raw': summary
                }
        
        return {'type': 'other', 'content': summary, 'raw': summary}

    def _parse_service_details(self, service_info):
        """
        Parse service details into structured format
        """
        import re
        
        service_data = {
            'name': service_info,
            'category': None,
            'type': None,
            'add_ons': [],
            'has_consultation': False,
            'raw': service_info
        }
        
        # Extract add-ons
        add_on_patterns = [
            'with Bottom Lash(as an add-on service)',
            'with Consulting Session', 
            'with Lash Removal',
            'with 2 add-ons',
            'with 3 add-ons'
        ]
        
        for pattern in add_on_patterns:
            if pattern in service_info:
                if 'Bottom Lash' in pattern:
                    service_data['add_ons'].append('Bottom Lash')
                elif 'Consulting Session' in pattern:
                    service_data['add_ons'].append('Consulting Session')
                    service_data['has_consultation'] = True
                elif 'Lash Removal' in pattern:
                    service_data['add_ons'].append('Lash Removal')
                elif '2 add-ons' in pattern:
                    service_data['add_ons'].append('Multiple (2)')
                elif '3 add-ons' in pattern:
                    service_data['add_ons'].append('Multiple (3)')
                
                service_info = service_info.replace(pattern, '').strip()
        
        # Determine service category
        if 'Classic' in service_info:
            service_data['category'] = 'Classic'
            if 'Fullset' in service_info:
                service_data['type'] = 'Fullset'
            elif 'Within' in service_info:
                service_data['type'] = 'Refill'
                if '2-week' in service_info:
                    service_data['refill_period'] = '2-week'
                elif '3-week' in service_info:
                    service_data['refill_period'] = '3-week'
                elif '4-week' in service_info:
                    service_data['refill_period'] = '4-week'
        
        elif 'Styling' in service_info:
            service_data['category'] = 'Styling'
            if 'Fullset' in service_info:
                service_data['type'] = 'Fullset'
            elif 'Within' in service_info:
                service_data['type'] = 'Refill'
                if '2-week' in service_info:
                    service_data['refill_period'] = '2-week'
                elif '3-week' in service_info:
                    service_data['refill_period'] = '3-week'
                elif '4-week' in service_info:
                    service_data['refill_period'] = '4-week'
        
        elif 'Volume' in service_info:
            service_data['category'] = 'Volume'
            if 'Fullset' in service_info:
                service_data['type'] = 'Fullset'
            elif 'Within' in service_info:
                service_data['type'] = 'Refill'
        
        elif 'Premium' in service_info:
            service_data['category'] = 'Premium'
            service_data['type'] = 'Fullset'
        
        elif 'Glue test' in service_info:
            service_data['category'] = 'Consultation'
            service_data['type'] = 'Glue Test'
        
        # Extract service codes (C01-C02, S01-S08, P01-P02)
        code_match = re.search(r'\(([CSPR]\d{2}-[CSPR]\d{2})\)', service_info)
        if code_match:
            service_data['service_code'] = code_match.group(1)
        
        return service_data

    def _validate_appointments(self, client_appointments, business, default_employee, 
                             create_missing_customers, create_missing_services, stats):
        """
        Validate appointments without saving (dry run mode)
        """
        from customers.models import Customer
        from services.models import Service
        
        for event in client_appointments:
            try:
                parsed = event['parsed']
                customer_name = parsed['customer_name']
                
                # Check if customer exists
                try:
                    customer = Customer.objects.get(
                        first_name__iexact=customer_name.split(' ')[0],
                        last_name__iexact=' '.join(customer_name.split(' ')[1:]) if len(customer_name.split(' ')) > 1 else '',
                        business=business
                    )
                    stats['matched_customers'] += 1
                except Customer.DoesNotExist:
                    if create_missing_customers:
                        stats['created_customers'] += 1
                        stats['warnings'].append(f"Will create new customer: {customer_name}")
                    else:
                        stats['errors'].append(f"Customer not found: {customer_name}")
                        continue
                
                # Check service
                service_name = f"{parsed['service']['category']} {parsed['service']['type']}"
                try:
                    service = Service.objects.get(name__icontains=service_name, business=business)
                    stats['matched_services'] += 1
                except Service.DoesNotExist:
                    if create_missing_services:
                        stats['created_services'] += 1
                        stats['warnings'].append(f"Will create new service: {service_name}")
                    else:
                        stats['errors'].append(f"Service not found: {service_name}")
                        continue
                
                stats['created_appointments'] += 1
                
            except Exception as e:
                stats['errors'].append(f"Error validating appointment {parsed.get('customer_name', 'Unknown')}: {str(e)}")
        
        return True

    def _import_appointments(self, client_appointments, business, default_employee,
                           skip_duplicates, create_missing_customers, create_missing_services, stats):
        """
        Actually import appointments to database
        """
        from customers.models import Customer
        from services.models import Service, ServiceCategory
        from appointments.models import Appointment, AppointmentService
        from datetime import datetime
        from django.utils import timezone
        import pytz
        
        for event in client_appointments:
            try:
                parsed = event['parsed']
                customer_name = parsed['customer_name']
                
                # Parse datetime from ICS format (20250709T170000Z)
                start_time_str = event['DTSTART']
                end_time_str = event['DTEND']
                
                # Convert from UTC to timezone-aware datetime
                start_time = datetime.strptime(start_time_str, '%Y%m%dT%H%M%SZ')
                end_time = datetime.strptime(end_time_str, '%Y%m%dT%H%M%SZ')
                start_time = pytz.UTC.localize(start_time)
                end_time = pytz.UTC.localize(end_time)
                
                # Get or create customer
                customer = None
                name_parts = customer_name.split(' ')
                first_name = name_parts[0] if name_parts else customer_name
                last_name = ' '.join(name_parts[1:]) if len(name_parts) > 1 else ''
                
                try:
                    customer = Customer.objects.get(
                        first_name__iexact=first_name,
                        last_name__iexact=last_name,
                        business=business
                    )
                    stats['matched_customers'] += 1
                except Customer.DoesNotExist:
                    if create_missing_customers:
                        customer = Customer.objects.create(
                            first_name=first_name,
                            last_name=last_name,
                            business=business,
                            email='',  # Will be empty, can be updated later
                            phone_number='',  # Will be empty, can be updated later
                        )
                        stats['created_customers'] += 1
                    else:
                        stats['errors'].append(f"Customer not found: {customer_name}")
                        continue
                
                # Get or create service
                service = None
                service_name = f"{parsed['service']['category']} {parsed['service']['type']}"
                if parsed['service'].get('refill_period'):
                    service_name += f" {parsed['service']['refill_period']}"
                
                try:
                    service = Service.objects.filter(
                        name__icontains=parsed['service']['category'],
                        business=business
                    ).first()
                    
                    if not service:
                        raise Service.DoesNotExist()
                    
                    stats['matched_services'] += 1
                except Service.DoesNotExist:
                    if create_missing_services:
                        # Get or create service category
                        category, _ = ServiceCategory.objects.get_or_create(
                            name=parsed['service']['category'],
                            business=business
                        )
                        
                        # Create service
                        service = Service.objects.create(
                            name=service_name,
                            business=business,
                            category=category,
                            base_duration=timezone.timedelta(hours=1),  # Default 1 hour
                            base_price=100.00,  # Default price, can be updated
                            description=f"Imported from calendar: {parsed['raw']}"
                        )
                        stats['created_services'] += 1
                    else:
                        stats['errors'].append(f"Service not found: {service_name}")
                        continue
                
                # Check for duplicate appointments
                if skip_duplicates:
                    existing_appointment = Appointment.objects.filter(
                        customer=customer,
                        employee=default_employee,
                        start_time=start_time,
                        status__in=['pending', 'confirmed']
                    ).first()
                    
                    if existing_appointment:
                        stats['skipped_appointments'] += 1
                        stats['warnings'].append(f"Skipped duplicate appointment for {customer_name} at {start_time}")
                        continue
                
                # Create appointment
                appointment = Appointment.objects.create(
                    customer=customer,
                    employee=default_employee,
                    start_time=start_time,
                    end_time=end_time,
                    status='confirmed',
                    notes=f"Imported from calendar: {parsed['raw']}"
                )
                
                # Create appointment service
                AppointmentService.objects.create(
                    appointment=appointment,
                    service=service,
                    employee=default_employee,
                    price=service.base_price
                )
                
                stats['created_appointments'] += 1
                
            except Exception as e:
                stats['errors'].append(f"Error importing appointment {parsed.get('customer_name', 'Unknown')}: {str(e)}")
        
        return True

