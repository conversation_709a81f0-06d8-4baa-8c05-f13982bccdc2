from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>

from .views import (
    CampaignViewSet, CampaignTypeViewSet, MarketingAnalyticsViewSet,
    MarketingCreditsAPIView, EmailImageUploadAPIView, EmailContentImageProcessorAPIView
)

router = DefaultRouter()
router.register(r'campaigns', CampaignViewSet, basename='campaign')
router.register(r'campaign-types', CampaignTypeViewSet, basename='campaign-type')
router.register(r'analytics', MarketingAnalyticsViewSet, basename='marketing-analytics')

urlpatterns = [
    path('', include(router.urls)),
    path('credits/', MarketingCreditsAPIView.as_view(), name='marketing-credits'),
    path('upload-image/', EmailImageUploadAPIView.as_view(), name='email-image-upload'),
    path('process-content/', EmailContentImageProcessorAPIView.as_view(), name='email-content-processor'),
]
