import re
import base64
import io
import uuid
from rest_framework import serializers
from marketing.models import (
    CampaignType, Campaign, CampaignChannel, CampaignRecipient, 
    MarketingAnalytics
)
from business.models import BusinessCustomer
from aws_services.storage_service import storage_service


class CampaignTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = CampaignType
        fields = '__all__'


class CampaignChannelSerializer(serializers.ModelSerializer):
    class Meta:
        model = CampaignChannel
        fields = '__all__'


class CampaignRecipientSerializer(serializers.ModelSerializer):
    user_email = serializers.CharField(source='user.email', read_only=True)
    user_name = serializers.SerializerMethodField()
    
    class Meta:
        model = CampaignRecipient
        fields = '__all__'
    
    def get_user_name(self, obj):
        return f"{obj.user.first_name} {obj.user.last_name}".strip()


class CampaignSerializer(serializers.ModelSerializer):
    channels = CampaignChannelSerializer(many=True, required=False)
    recipients = CampaignRecipientSerializer(many=True, read_only=True)
    recipients_count = serializers.SerializerMethodField()
    campaign_type_name = serializers.CharField(source='campaign_type.name', read_only=True)
    created_by_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Campaign
        fields = '__all__'
        read_only_fields = ['created_by', 'sent_count', 'open_count', 'click_count', 'conversion_count']
    
    def get_recipients_count(self, obj):
        return obj.recipients.count()
    
    def get_created_by_name(self, obj):
        if obj.created_by:
            return f"{obj.created_by.first_name} {obj.created_by.last_name}".strip()
        return None
    
    def create(self, validated_data):
        channels_data = validated_data.pop('channels', [])
        
        # Create campaign
        campaign = Campaign.objects.create(**validated_data)
        
        # Create channels
        for channel_data in channels_data:
            # Process content to convert base64 images to S3 URLs
            content = channel_data.get('content', '')
            raw_components = channel_data.get('raw_components', [])
            
            # Process base64 images in content
            if content:
                processed_content, uploaded_images = self._process_content_images(content)
                channel_data['content'] = processed_content
            
            # Create channel
            CampaignChannel.objects.create(campaign=campaign, **channel_data)
        
        return campaign
    
    def update(self, instance, validated_data):
        channels_data = validated_data.pop('channels', None)
        
        # Update campaign fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Update channels if provided
        if channels_data is not None:
            # Clear existing channels
            instance.channels.all().delete()
            
            # Create new channels
            for channel_data in channels_data:
                # Process content to convert base64 images to S3 URLs
                content = channel_data.get('content', '')
                if content:
                    processed_content, uploaded_images = self._process_content_images(content)
                    channel_data['content'] = processed_content
                
                CampaignChannel.objects.create(campaign=instance, **channel_data)
        
        return instance
    
    def _process_content_images(self, content):
        """Process base64 images in content and convert to S3 URLs"""
        uploaded_images = []
        
        # Find all base64 images
        base64_pattern = r'data:image/[^;]+;base64,([^"\']+)'
        matches = re.findall(base64_pattern, content)
        
        processed_content = content
        
        for match in matches:
            try:
                # Decode base64 image
                image_bytes = base64.b64decode(match)
                filename = f"campaign_content_{uuid.uuid4().hex[:8]}.jpg"
                
                # Create a file-like object
                from django.core.files.uploadedfile import InMemoryUploadedFile
                image_file = InMemoryUploadedFile(
                    io.BytesIO(image_bytes),
                    None,
                    filename,
                    'image/jpeg',
                    len(image_bytes),
                    None
                )
                
                # Upload to S3
                file_path = f"marketing/campaigns/{filename}"
                file_url = storage_service.upload_file(image_file, file_path)
                
                # Replace base64 with S3 URL
                old_src = f"data:image/jpeg;base64,{match}"
                processed_content = processed_content.replace(old_src, file_url)
                
                uploaded_images.append({
                    'filename': filename,
                    'url': file_url
                })
                
            except Exception as e:
                # Log error but continue processing
                print(f"Failed to process image: {str(e)}")
                continue
        
        return processed_content, uploaded_images


class MarketingAnalyticsSerializer(serializers.ModelSerializer):
    campaign_name = serializers.CharField(source='campaign.name', read_only=True)
    
    class Meta:
        model = MarketingAnalytics
        fields = '__all__'
