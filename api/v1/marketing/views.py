import base64
import io
import uuid
import re
from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.authentication import TokenAuthentication, SessionAuthentication
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication
from django_filters.rest_framework import DjangoFilterBackend
from django.db import models
from django.db.models import Prefetch
from django.shortcuts import get_object_or_404
from django.core.files.uploadedfile import InMemoryUploadedFile

from marketing.models import (
    CampaignType, Campaign, CampaignChannel, CampaignRecipient, 
    BusinessCreditUsage, MarketingAnalytics
)
from .serializers import (
    CampaignTypeSerializer, CampaignSerializer, CampaignChannelSerializer, 
    CampaignRecipientSerializer, MarketingAnalyticsSerializer
)
from business.models import BusinessCustomer
from customers.models import CustomerProfile
from employees.models import Employee
from accounts.models import User
from aws_services.storage_service import storage_service


class CampaignTypeViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing campaign types
    """
    serializer_class = CampaignTypeSerializer
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication, SessionAuthentication, TokenAuthentication]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']

    def get_queryset(self):
        return CampaignType.objects.all()


class CampaignViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing marketing campaigns
    """
    serializer_class = CampaignSerializer
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication, SessionAuthentication, TokenAuthentication]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    filterset_fields = ['status', 'active', 'campaign_type']
    ordering_fields = ['name', 'created_at', 'scheduled_time']
    ordering = ['-created_at']

    def get_queryset(self):
        return Campaign.objects.select_related(
            'campaign_type', 'created_by'
        ).prefetch_related('channels', 'recipients')

    def perform_create(self, serializer):
        """
        自动设置创建者为当前用户
        """
        serializer.save(created_by=self.request.user)

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get campaign statistics"""
        campaigns = self.get_queryset()
        
        stats = {
            'total_campaigns': campaigns.count(),
            'draft_campaigns': campaigns.filter(status='draft').count(),
            'scheduled_campaigns': campaigns.filter(status='scheduled').count(),
            'in_progress_campaigns': campaigns.filter(status='in_progress').count(),
            'completed_campaigns': campaigns.filter(status='completed').count(),
            'cancelled_campaigns': campaigns.filter(status='cancelled').count(),
            'total_recipients': sum(campaign.total_recipients for campaign in campaigns),
            'total_sent': sum(campaign.sent_count for campaign in campaigns),
            'total_opened': sum(campaign.open_count for campaign in campaigns),
            'total_clicked': sum(campaign.click_count for campaign in campaigns),
        }
        
        return Response(stats)

    @action(detail=True, methods=['get'])
    def recipients(self, request, pk=None):
        """Get campaign recipients list"""
        campaign = self.get_object()
        recipients = campaign.recipients.all()
        
        page = self.paginate_queryset(recipients)
        if page is not None:
            serializer = CampaignRecipientSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = CampaignRecipientSerializer(recipients, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def add_recipients(self, request, pk=None):
        """Add recipients to campaign"""
        campaign = self.get_object()
        user_ids = request.data.get('recipients', [])
        
        if not user_ids:
            return Response(
                {'error': 'No recipients provided'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        added_count = 0
        errors = []
        
        for user_id in user_ids:
            try:
                user = User.objects.get(id=user_id)
                # Check if recipient already exists
                if not campaign.recipients.filter(user=user).exists():
                    CampaignRecipient.objects.create(
                        campaign=campaign,
                        user=user,
                        matched_by_rule=False
                    )
                    added_count += 1
                else:
                    errors.append(f'User {user_id} is already a recipient')
            except User.DoesNotExist:
                errors.append(f'User {user_id} does not exist')
        
        # Update campaign recipient count
        campaign.total_recipients = campaign.recipients.count()
        campaign.save(update_fields=['total_recipients'])
        
        return Response({
            'added_count': added_count,
            'total_recipients': campaign.total_recipients,
            'errors': errors
        })
    
    @action(detail=True, methods=['post'])
    def remove_recipients(self, request, pk=None):
        """Remove recipients from campaign"""
        campaign = self.get_object()
        user_ids = request.data.get('recipients', [])
        
        if not user_ids:
            return Response(
                {'error': 'No recipients provided'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        removed_count = 0
        
        for user_id in user_ids:
            try:
                campaign.recipients.filter(user_id=user_id).delete()
                removed_count += 1
            except Exception as e:
                pass  # User doesn't exist in recipients
        
        # Update campaign recipient count
        campaign.total_recipients = campaign.recipients.count()
        campaign.save(update_fields=['total_recipients'])
        
        return Response({
            'removed_count': removed_count,
            'total_recipients': campaign.total_recipients
        })

    @action(detail=True, methods=['post'])
    def preview(self, request, pk=None):
        """Send campaign preview email"""
        campaign = self.get_object()
        email_addresses = request.data.get('email_addresses', [])
        
        if not email_addresses:
            return Response(
                {'error': 'No email addresses provided'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Here you would implement the actual email sending logic
        # For now, we'll just return a success response
        
        return Response({
            'message': f'Preview email sent to {len(email_addresses)} addresses',
            'recipients': email_addresses
        })


class MarketingAnalyticsViewSet(viewsets.ModelViewSet):
    """
    ViewSet for marketing analytics
    """
    serializer_class = MarketingAnalyticsSerializer
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication, SessionAuthentication, TokenAuthentication]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['campaign', 'date']
    ordering_fields = ['date', 'created_at']
    ordering = ['-date']

    def get_queryset(self):
        return MarketingAnalytics.objects.select_related('campaign')


class MarketingCreditsAPIView(APIView):
    """
    API for managing marketing credits
    """
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication, SessionAuthentication, TokenAuthentication]
    
    def get(self, request):
        """Get business marketing credits"""
        try:
            # Get business_id from query parameters or use user's default business
            business_id = request.query_params.get('business_id')
            
            if business_id:
                # Get specific business by ID
                from business.models import Business
                try:
                    business = Business.objects.get(id=business_id)
                except Business.DoesNotExist:
                    return Response(
                        {'error': f'Business with id {business_id} does not exist'}, 
                        status=status.HTTP_404_NOT_FOUND
                    )
            else:
                # Use user's first business membership as default
                business_membership = request.user.business_memberships.first()
                if not business_membership:
                    # If no business memberships, create a default business for development
                    from business.models import Business
                    business, created = Business.objects.get_or_create(
                        name="Default Business",
                        defaults={
                            'slug': 'default-business',
                            'owner': request.user,
                        }
                    )
                    if created:
                        # Create a business membership for the user
                        from business.models import BusinessUser, AccessLevel
                        access_level, _ = AccessLevel.objects.get_or_create(
                            business=business,
                            name="Owner",
                            defaults={'level': 100, 'is_admin': True}
                        )
                        BusinessUser.objects.create(
                            business=business,
                            user=request.user,
                            access_level=access_level,
                            is_primary=True
                        )
                else:
                    business = business_membership.business
            
            # Get or create credit usage record
            credit_usage, created = BusinessCreditUsage.objects.get_or_create(
                business=business,
                defaults={
                    'email_credits_used': 0,
                    'email_credit_limit': 1000,
                    'text_credits_used': 0,
                    'text_credit_limit': 0
                }
            )
            
            data = {
                'email_credits_used': credit_usage.email_credits_used,
                'email_credit_limit': credit_usage.email_credit_limit,
                'email_credits_remaining': credit_usage.email_credit_limit - credit_usage.email_credits_used,
                'text_credits_used': credit_usage.text_credits_used,
                'text_credit_limit': credit_usage.text_credit_limit,
                'text_credits_remaining': credit_usage.text_credit_limit - credit_usage.text_credits_used,
            }
            
            return Response(data)
            
        except Exception as e:
            return Response(
                {'error': f'Failed to get marketing credits: {str(e)}'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class EmailImageUploadAPIView(APIView):
    """
    API for uploading base64 images to S3 and returning URLs
    """
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication, SessionAuthentication, TokenAuthentication]
    
    def post(self, request):
        """Upload base64 image to S3"""
        try:
            image_data = request.data.get('image_data')
            filename = request.data.get('filename')
            
            if not image_data:
                return Response(
                    {'error': 'No image data provided'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Remove data URL prefix if present
            if ',' in image_data:
                header, image_data = image_data.split(',', 1)
            
            # Decode base64 image
            try:
                image_bytes = base64.b64decode(image_data)
            except Exception as e:
                return Response(
                    {'error': f'Invalid base64 image data: {str(e)}'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Generate filename if not provided
            if not filename:
                filename = f"email_image_{uuid.uuid4().hex[:8]}.jpg"
            
            # Create InMemoryUploadedFile
            image_file = InMemoryUploadedFile(
                io.BytesIO(image_bytes),
                None,
                filename,
                'image/jpeg',
                len(image_bytes),
                None
            )
            
            # Upload to S3
            file_path = f"marketing/images/{filename}"
            file_url = storage_service.upload_file(image_file, file_path)
            
            return Response({
                'url': file_url,
                'filename': filename,
                'message': 'Image uploaded successfully'
            })
            
        except Exception as e:
            return Response(
                {'error': f'Failed to upload image: {str(e)}'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class EmailContentImageProcessorAPIView(APIView):
    """
    API for processing email content and converting base64 images to S3 URLs
    """
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication, SessionAuthentication, TokenAuthentication]
    
    def post(self, request):
        """Process email content and convert base64 images to S3 URLs"""
        try:
            content = request.data.get('content', '')
            components = request.data.get('components', [])
            
            if not content:
                return Response(
                    {'error': 'No content provided'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Find all base64 images in content
            base64_pattern = r'data:image/[^;]+;base64,([^"]+)'
            matches = re.findall(base64_pattern, content)
            
            processed_content = content
            processed_components = components.copy() if components else []
            uploaded_images = []
            
            for match in matches:
                try:
                    # Decode and upload image
                    image_bytes = base64.b64decode(match)
                    filename = f"email_content_{uuid.uuid4().hex[:8]}.jpg"
                    
                    image_file = InMemoryUploadedFile(
                        io.BytesIO(image_bytes),
                        None,
                        filename,
                        'image/jpeg',
                        len(image_bytes),
                        None
                    )
                    
                    file_path = f"marketing/content/{filename}"
                    file_url = storage_service.upload_file(image_file, file_path)
                    
                    # Replace in content
                    old_src = f"data:image/jpeg;base64,{match}"
                    processed_content = processed_content.replace(old_src, file_url)
                    
                    uploaded_images.append({
                        'filename': filename,
                        'url': file_url
                    })
                    
                except Exception as e:
                    print(f"Failed to process image: {str(e)}")
                    continue
            
            return Response({
                'content': processed_content,
                'components': processed_components,
                'uploaded_images': uploaded_images,
                'message': f'Processed {len(uploaded_images)} images'
            })
            
        except Exception as e:
            return Response(
                {'error': f'Failed to process content: {str(e)}'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
