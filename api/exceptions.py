"""
Custom exception handlers for Django REST Framework.
"""
import logging
import traceback
import uuid
from datetime import datetime

from django.conf import settings
from django.core.exceptions import ValidationError as DjangoValidationError
from django.http import Http404
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import exception_handler as drf_exception_handler
from rest_framework.exceptions import (
    ValidationError, 
    AuthenticationFailed, 
    PermissionDenied,
    NotFound,
    MethodNotAllowed,
    Throttled
)

logger = logging.getLogger('api')


def custom_exception_handler(exc, context):
    """
    Custom exception handler that provides consistent error responses
    and detailed logging for all API errors.
    """
    # Generate unique error ID for tracking
    error_id = str(uuid.uuid4())[:8]
    
    # Get the request from context
    request = context.get('request')
    view = context.get('view')
    
    # Get user information
    user_info = "Anonymous"
    if request and hasattr(request, 'user') and request.user.is_authenticated:
        user_info = f"{request.user.email} (ID: {request.user.id})"
    
    # Collect context information
    error_context = {
        'error_id': error_id,
        'exception_type': type(exc).__name__,
        'view': view.__class__.__name__ if view else 'Unknown',
        'user': user_info,
        'timestamp': datetime.now().isoformat(),
    }
    
    if request:
        error_context.update({
            'method': request.method,
            'path': request.path,
            'user_agent': request.META.get('HTTP_USER_AGENT', 'Unknown'),
            'remote_addr': _get_client_ip(request),
        })
    
    # Call DRF's default exception handler first
    response = drf_exception_handler(exc, context)

    if response is not None:
        # DRF handled the exception
        custom_response_data = _create_error_response(exc, error_id, response.data, response.status_code)

        # Log the error with appropriate level
        log_level = _get_log_level_for_status(response.status_code)

        # For validation errors, always log request body to help debugging
        extra_context = {
            'error_context': error_context,
            'response_data': custom_response_data,
            'status_code': response.status_code,
        }

        # Add request body for validation errors (400 status)
        if response.status_code == 400 and hasattr(context.get('request'), 'data'):
            try:
                request_data = dict(context['request'].data)
                # Filter sensitive fields but keep most data for debugging
                filtered_data = _filter_sensitive_data(request_data)
                extra_context['request_body'] = filtered_data

                # Also print to console for immediate visibility during development
                print(f"\n🔍 VALIDATION ERROR [{error_id}]:")
                print(f"   Endpoint: {context['request'].method} {context['request'].path}")
                print(f"   Request Body: {filtered_data}")
                print(f"   Validation Errors: {response.data}")
                print(f"   Stack Trace: {traceback.format_exc()}\n")

            except Exception as e:
                extra_context['request_body_error'] = f"Could not parse request data: {e}"

        logger.log(
            log_level,
            f"API Error [{error_id}]: {type(exc).__name__} - {str(exc)}",
            extra=extra_context,
            exc_info=log_level >= logging.ERROR
        )
        
        response.data = custom_response_data
        
    else:
        # DRF didn't handle it, create our own response
        if isinstance(exc, DjangoValidationError):
            custom_response_data = _create_error_response(
                exc, error_id, {'detail': str(exc)}, status.HTTP_400_BAD_REQUEST
            )
            response = Response(custom_response_data, status=status.HTTP_400_BAD_REQUEST)
        
        elif isinstance(exc, Http404):
            custom_response_data = _create_error_response(
                exc, error_id, {'detail': 'Not found'}, status.HTTP_404_NOT_FOUND
            )
            response = Response(custom_response_data, status=status.HTTP_404_NOT_FOUND)
        
        else:
            # Unhandled exception - log as error and return 500
            custom_response_data = _create_error_response(
                exc, error_id, {'detail': 'Internal server error'}, status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            response = Response(custom_response_data, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        # Log unhandled exceptions as errors
        logger.error(
            f"Unhandled API Exception [{error_id}]: {type(exc).__name__} - {str(exc)}",
            extra={
                'error_context': error_context,
                'response_data': custom_response_data,
            },
            exc_info=True
        )
    
    return response


def _create_error_response(exc, error_id, original_data, status_code):
    """
    Create a standardized error response format.
    """
    # Base error response structure
    error_response = {
        'error': True,
        'error_id': error_id,
        'timestamp': datetime.now().isoformat(),
        'status_code': status_code,
    }
    
    # Add user-friendly message based on exception type
    if isinstance(exc, ValidationError):
        error_response['message'] = 'Validation failed'
        error_response['details'] = original_data
    
    elif isinstance(exc, AuthenticationFailed):
        error_response['message'] = 'Authentication failed'
        error_response['details'] = 'Please check your credentials and try again'
    
    elif isinstance(exc, PermissionDenied):
        error_response['message'] = 'Permission denied'
        error_response['details'] = 'You do not have permission to perform this action'
    
    elif isinstance(exc, NotFound):
        error_response['message'] = 'Resource not found'
        error_response['details'] = 'The requested resource could not be found'
    
    elif isinstance(exc, MethodNotAllowed):
        error_response['message'] = 'Method not allowed'
        error_response['details'] = f'The {exc.method} method is not allowed for this endpoint'
    
    elif isinstance(exc, Throttled):
        error_response['message'] = 'Rate limit exceeded'
        error_response['details'] = f'Please wait {exc.wait} seconds before trying again'
    
    else:
        # Generic error message
        if status_code >= 500:
            error_response['message'] = 'Internal server error'
            error_response['details'] = 'An unexpected error occurred. Please try again later.'
        else:
            error_response['message'] = str(exc) if str(exc) else 'An error occurred'
            error_response['details'] = original_data if isinstance(original_data, dict) else {'detail': str(original_data)}
    
    # In debug mode, include more technical details
    if settings.DEBUG:
        error_response['debug_info'] = {
            'exception_type': type(exc).__name__,
            'exception_message': str(exc),
            'original_data': original_data,
        }
    
    return error_response


def _get_log_level_for_status(status_code):
    """
    Determine appropriate log level based on HTTP status code.
    """
    if status_code >= 500:
        return logging.ERROR
    elif status_code >= 400:
        return logging.WARNING
    else:
        return logging.INFO


def _get_client_ip(request):
    """Get the client's IP address."""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def _filter_sensitive_data(data):
    """Filter out sensitive data from request data for logging."""
    if not isinstance(data, dict):
        return data

    sensitive_fields = {
        'password', 'token', 'secret', 'key', 'authorization',
        'csrf_token', 'csrfmiddlewaretoken', 'signature_data'
    }

    filtered = {}
    for key, value in data.items():
        key_lower = key.lower()
        if any(sensitive in key_lower for sensitive in sensitive_fields):
            filtered[key] = '[FILTERED]'
        elif isinstance(value, dict):
            filtered[key] = _filter_sensitive_data(value)
        elif isinstance(value, list) and value and isinstance(value[0], dict):
            filtered[key] = [_filter_sensitive_data(item) for item in value]
        else:
            filtered[key] = value

    return filtered


# Custom exception classes for specific use cases
class FileProcessingError(Exception):
    """Exception raised when file processing fails."""
    pass


class S3UploadError(Exception):
    """Exception raised when S3 upload fails."""
    pass


class AuthenticationError(Exception):
    """Exception raised when authentication fails."""
    pass


class ConfigurationError(Exception):
    """Exception raised when configuration is invalid."""
    pass
