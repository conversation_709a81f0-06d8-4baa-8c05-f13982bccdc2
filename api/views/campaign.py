from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q
from django.shortcuts import get_object_or_404

from accounts.models import User
from business.models import BusinessCustomer
from employees.models import Employee
from customers.models import CustomerProfile


class CampaignRecipientsAPIView(APIView):
    """
    API endpoint for retrieving potential recipients for campaigns.
    Returns customers and employees associated with the current user's business.
    
    GET /api/campaign/recipients/
    
    Query Parameters:
    - type: Filter by recipient type ('all', 'customers', 'employees')
    - q: Search term to filter recipients by name, email, or phone number
    - opted_out: If true, returns only opted-out customers
    
    Returns:
    - business_id: The ID of the current user's business
    - business_name: The name of the current user's business
    - customers: List of customer objects
    - employees: List of employee objects
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request, *args, **kwargs):
        try:
            # Get the current user's employee profile
            employee = get_object_or_404(Employee, user=request.user)
            business = employee.business
            
            # Get query parameters
            recipient_type = request.query_params.get('type', 'all')  # 'all', 'customers', 'employees'
            search_term = request.query_params.get('q', '')
            opted_out = request.query_params.get('opted_out', '').lower() == 'true'
            
            response_data = {
                'business_id': str(business.id),
                'business_name': business.name,
                'customers': [],
                'employees': []
            }
            
            # Query customers
            if recipient_type in ['all', 'customers']:
                business_customers_query = BusinessCustomer.objects.filter(
                    business=business
                ).select_related('customer__user')
                
                # Apply search filter
                if search_term:
                    business_customers_query = business_customers_query.filter(
                        Q(customer__user__email__icontains=search_term) | 
                        Q(customer__user__first_name__icontains=search_term) | 
                        Q(customer__user__last_name__icontains=search_term) | 
                        Q(customer__user__phone_number__icontains=search_term)
                    )
                
                # Filter by opt-in status if requested
                if opted_out:
                    business_customers_query = business_customers_query.filter(opt_in_marketing=False)
                
                # Limit the number of returned customers
                business_customers = business_customers_query[:100]
                
                # Format customer data
                for bc in business_customers:
                    user = bc.customer.user
                    response_data['customers'].append({
                        'id': str(user.id),
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'phone_number': str(user.phone_number),
                        'business_customer_id': str(bc.id),
                        'opt_in_marketing': bc.opt_in_marketing,
                        'email_reminders': bc.email_reminders,
                        'sms_reminders': bc.sms_reminders
                    })
            
            # Query employees
            if recipient_type in ['all', 'employees']:
                employees_query = Employee.objects.filter(
                    business=business,
                    is_active=True
                ).select_related('user')
                
                # Apply search filter
                if search_term:
                    employees_query = employees_query.filter(
                        Q(user__email__icontains=search_term) | 
                        Q(user__first_name__icontains=search_term) | 
                        Q(user__last_name__icontains=search_term) | 
                        Q(user__phone_number__icontains=search_term)
                    )
                
                # Format employee data
                for emp in employees_query:
                    user = emp.user
                    response_data['employees'].append({
                        'id': str(user.id),
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'phone_number': str(user.phone_number),
                        'employee_id': str(emp.id),
                        'employee_type': emp.employee_type
                    })
            
            return Response(response_data)
            
        except Exception as e:
            return Response({
                'error': 'Error retrieving campaign recipients',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 