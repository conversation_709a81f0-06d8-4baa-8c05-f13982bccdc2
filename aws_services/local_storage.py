"""
Local file storage service as a fallback when S3 is not available.
This is useful for development environments where AWS credentials are not configured.
"""
import os
import uuid
import logging
from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from typing import BinaryIO

logger = logging.getLogger(__name__)


class LocalStorageService:
    """Local file storage service that mimics S3 service interface."""
    
    def __init__(self):
        self.media_root = getattr(settings, 'LOCAL_MEDIA_ROOT', 'media/uploads')
        self.base_url = getattr(settings, 'MEDIA_URL', '/media/')
        
        # Ensure media directory exists
        full_path = os.path.join(settings.BASE_DIR, self.media_root)
        os.makedirs(full_path, exist_ok=True)
        
        logger.info(f"LocalStorageService initialized with media_root: {self.media_root}")
    
    def upload_file(self, file_obj: BinaryIO, key: str, content_type: str = None, metadata: dict = None) -> str:
        """
        Upload a file to local storage.
        
        Args:
            file_obj: File object to upload
            key: File path/key
            content_type: MIME type of the file
            metadata: Additional metadata (ignored in local storage)
            
        Returns:
            Local URL of the uploaded file
        """
        try:
            # Ensure the directory exists
            file_path = os.path.join(self.media_root, key)
            dir_path = os.path.dirname(file_path)
            full_dir_path = os.path.join(settings.BASE_DIR, dir_path)
            os.makedirs(full_dir_path, exist_ok=True)
            
            # Reset file pointer
            file_obj.seek(0)
            
            # Save file using Django's default storage
            saved_path = default_storage.save(file_path, ContentFile(file_obj.read()))
            
            # Generate URL
            url = self._get_file_url(saved_path)
            
            logger.info(f"File uploaded successfully to local storage: {url}")
            return url
            
        except Exception as e:
            logger.error(f"Failed to upload file to local storage: {e}")
            raise
    
    def download_file(self, key: str) -> str:
        """
        Download a file from local storage to a temporary location.
        
        Args:
            key: File path/key
            
        Returns:
            Path to the local file
        """
        try:
            file_path = os.path.join(settings.BASE_DIR, self.media_root, key)
            
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {key}")
            
            return file_path
            
        except Exception as e:
            logger.error(f"Failed to download file from local storage: {e}")
            raise
    
    def get_file_info(self, key: str) -> dict:
        """
        Get metadata about a file in local storage.
        
        Args:
            key: File path/key
            
        Returns:
            Dictionary with file metadata
        """
        try:
            file_path = os.path.join(settings.BASE_DIR, self.media_root, key)
            
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {key}")
            
            stat = os.stat(file_path)
            
            return {
                'size': stat.st_size,
                'last_modified': stat.st_mtime,
                'content_type': self._guess_content_type(file_path),
                'metadata': {},
                'etag': str(hash(file_path)),  # Simple hash as etag
            }
            
        except Exception as e:
            logger.error(f"Failed to get file info from local storage: {e}")
            raise
    
    def delete_file(self, key: str) -> bool:
        """
        Delete a file from local storage.
        
        Args:
            key: File path/key
            
        Returns:
            True if successful
        """
        try:
            file_path = os.path.join(settings.BASE_DIR, self.media_root, key)
            
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"File deleted successfully: {key}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete file from local storage: {e}")
            raise
    
    def file_exists(self, key: str) -> bool:
        """
        Check if a file exists in local storage.
        
        Args:
            key: File path/key
            
        Returns:
            True if file exists, False otherwise
        """
        file_path = os.path.join(settings.BASE_DIR, self.media_root, key)
        return os.path.exists(file_path)
    
    def generate_presigned_url(self, key: str, expiration: int = 3600, method: str = 'get_object') -> str:
        """
        Generate a URL for local file access (no expiration for local files).
        
        Args:
            key: File path/key
            expiration: Ignored for local storage
            method: Ignored for local storage
            
        Returns:
            Local file URL
        """
        file_path = os.path.join(self.media_root, key)
        return self._get_file_url(file_path)
    
    def _get_file_url(self, file_path: str) -> str:
        """Generate URL for a local file."""
        # Normalize path separators for URLs
        url_path = file_path.replace('\\', '/')

        # Remove media_root prefix if present to avoid duplication
        if url_path.startswith(self.media_root):
            url_path = url_path[len(self.media_root):].lstrip('/')

        # For development, return a local URL
        base_url = getattr(settings, 'BASE_URL', 'http://localhost:8000')
        return f"{base_url.rstrip('/')}/media/{url_path}"
    
    def _guess_content_type(self, file_path: str) -> str:
        """Guess content type from file extension."""
        import mimetypes
        content_type, _ = mimetypes.guess_type(file_path)
        return content_type or 'application/octet-stream'


# Create service instance
local_storage_service = LocalStorageService()
