import boto3
import logging
from botocore.exceptions import ClientError
from django.conf import settings
from typing import List, Dict, Any, Optional, Union

logger = logging.getLogger(__name__)

class SESEmailService:
    """
    Service class for sending emails using Amazon SES via boto3.
    """
    
    def __init__(self):
        """Initialize the SES client."""
        self.region_name = settings.AWS_SES_REGION_NAME
        self.sender_email = settings.AWS_SES_SENDER_EMAIL
        self.sender_name = settings.AWS_SES_SENDER_NAME
        self.configuration_set = settings.AWS_SES_CONFIGURATION_SET
        
        # Debug: Print AWS credentials information
        print("=== AWS Credentials Debug Info ===")
        print(f"AWS_ACCESS_KEY_ID: {settings.AWS_ACCESS_KEY_ID}")
        print(f"AWS_SECRET_ACCESS_KEY: {settings.AWS_SECRET_ACCESS_KEY}")
        print(f"AWS_SESSION_TOKEN: {settings.AWS_SESSION_TOKEN}")
        print(f"AWS_PROFILE: {settings.AWS_PROFILE or 'Not set'}")
        print(f"AWS_SES_REGION_NAME: {self.region_name}")
        print(f"AWS_SES_SENDER_EMAIL: {self.sender_email}")
        print("==================================")
        
        # Initialize SES client
        # Create boto3 client directly or with session based on available credentials
        if settings.AWS_ACCESS_KEY_ID and settings.AWS_SECRET_ACCESS_KEY:
            print("Using explicit AWS credentials (access key + secret)")
            self.client = boto3.client(
                'sesv2',
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                aws_session_token=settings.AWS_SESSION_TOKEN,  # 添加session token支持
                region_name=self.region_name,
            )
        elif settings.AWS_PROFILE:
            print(f"Using AWS profile: {settings.AWS_PROFILE}")
            # Create a session with the profile
            session = boto3.Session(profile_name=settings.AWS_PROFILE)
            self.client = session.client('sesv2', region_name=self.region_name)
        else:
            print("Using default AWS credentials (IAM role, etc.)")
            # Use default credentials (EC2 instance profile, etc.)
            self.client = boto3.client('sesv2', region_name=self.region_name)
    
    def send_email_v2(
        self,
        to_addresses,
        subject,
        html_content,
        text_content=None,
        cc_addresses=None,
        bcc_addresses=None,
        reply_to_addresses=None,
        sender_email=None,
        sender_name=None,
    ):
        # Debug: Print email sending information
        print("=== Email Sending Debug Info ===")
        print(f"To addresses: {to_addresses}")
        print(f"Subject: {subject}")
        print(f"Sender email: {sender_email or self.sender_email}")
        print(f"CC addresses: {cc_addresses}")
        print(f"BCC addresses: {bcc_addresses}")
        print(f"Reply to addresses: {reply_to_addresses}")
        print("================================")
        
        if isinstance(to_addresses, str):
            to_addresses = [to_addresses]
        to_addresses = [e.strip() for e in to_addresses if isinstance(e, str) and e.strip()]

        destination = {
            'ToAddresses': to_addresses
        }
        if cc_addresses:
            destination['CcAddresses'] = cc_addresses
        if bcc_addresses:
            destination['BccAddresses'] = bcc_addresses

        content = {
            'Simple': {
                'Subject': {
                    'Data': subject,
                    'Charset': 'UTF-8'
                },
                'Body': {
                    'Html': {
                        'Data': html_content,
                        'Charset': 'UTF-8'
                    }
                }
            }
        }
        if text_content:
            content['Simple']['Body']['Text'] = {
                'Data': text_content,
                'Charset': 'UTF-8'
            }

        from_email = sender_email or self.sender_email

        print(f"Attempting to send email via SES...")
        try:
            response = self.client.send_email(
                FromEmailAddress=from_email,
                Destination=destination,
                Content=content,
                ReplyToAddresses=reply_to_addresses or []
            )
            print(f"Email sent successfully! Message ID: {response.get('MessageId', 'Unknown')}")
            return response
        except ClientError as e:
            print(f"Failed to send email: {e}")
            print(f"Error code: {e.response['Error']['Code']}")
            print(f"Error message: {e.response['Error']['Message']}")
            raise
        except Exception as e:
            print(f"Unexpected error sending email: {e}")
            raise
    
    def send_campaign_preview(
        self,
        to_addresses: Union[str, List[str]],
        subject: str,
        html_content: str,
        campaign_name: str,
        text_content: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Send a campaign preview email.
        
        Args:
            to_addresses: Email address(es) to send the preview to.
            subject: Email subject line.
            html_content: HTML content of the email.
            campaign_name: Name of the campaign for tracking.
            text_content: Plain text content of the email (optional).
            
        Returns:
            Dict containing the response from SES.
        """
        # Add preview prefix to subject
        preview_subject = f"[PREVIEW] {subject}"
        
        # Add preview header to HTML content
        preview_html = f"""
        <div style="background-color: #f8f9fa; padding: 10px; margin-bottom: 20px; border: 1px solid #ddd; border-radius: 4px;">
            <p style="margin: 0; color: #666;">
                <strong>PREVIEW:</strong> This is a preview of the campaign "{campaign_name}". 
                The actual email will be sent from {self.sender_email}.
            </p>
        </div>
        {html_content}
        """
        
        # Add preview header to text content if provided
        preview_text = None
        if text_content:
            preview_text = f"PREVIEW: This is a preview of the campaign \"{campaign_name}\".\n\n{text_content}"
        
        # Send the preview email
        return self.send_email_v2(
            to_addresses=to_addresses,
            subject=preview_subject,
            html_content=preview_html,
            text_content=preview_text
        )


# Create a singleton instance
email_service = SESEmailService() 