"""
Unified Storage Service

Provides a unified interface for file storage that can use either S3 or local storage
depending on configuration and availability.
"""
import os
import logging
from typing import BinaryIO
from django.conf import settings

logger = logging.getLogger(__name__)


class UnifiedStorageService:
    """
    Unified storage service that automatically chooses between S3 and local storage.
    """
    
    def __init__(self):
        self._storage_backend = None
        self._initialize_backend()
    
    def _initialize_backend(self):
        """Initialize the appropriate storage backend."""
        use_local = os.getenv('USE_LOCAL_STORAGE', 'false').lower() == 'true'
        
        if use_local:
            logger.info("Using local storage backend")
            self._use_local_storage()
        else:
            # Try to use S3 first
            try:
                from .s3 import s3_service
                # Test S3 connection
                s3_service.client.list_buckets()
                self._storage_backend = s3_service
                self._backend_type = 's3'

                # Check if CloudFront is configured
                from config.aws import aws_config
                if aws_config.use_cloudfront:
                    logger.info(f"Using S3 storage backend with CloudFront CDN: {aws_config.cloudfront_domain}")
                else:
                    logger.info("Using S3 storage backend (direct S3 URLs)")
            except Exception as e:
                logger.warning(f"S3 not available ({e}), falling back to local storage")
                self._use_local_storage()
    
    def _use_local_storage(self):
        """Initialize local storage backend."""
        from .local_storage import local_storage_service
        self._storage_backend = local_storage_service
        self._backend_type = 'local'
    
    def upload_file(self, file_obj: BinaryIO, key: str, content_type: str = None, metadata: dict = None) -> str:
        """
        Upload a file using the configured storage backend.
        
        Args:
            file_obj: File object to upload
            key: Storage key/path for the file
            content_type: MIME type of the file
            metadata: Additional metadata
            
        Returns:
            URL of the uploaded file
        """
        try:
            return self._storage_backend.upload_file(file_obj, key, content_type, metadata)
        except Exception as e:
            logger.error(f"Failed to upload file with {self._backend_type} backend: {e}")
            
            # If S3 fails, try local storage as fallback
            if self._backend_type == 's3':
                logger.info("Attempting fallback to local storage")
                try:
                    self._use_local_storage()
                    return self._storage_backend.upload_file(file_obj, key, content_type, metadata)
                except Exception as fallback_error:
                    logger.error(f"Fallback to local storage also failed: {fallback_error}")
            
            raise
    
    def download_file(self, key: str) -> str:
        """Download a file and return local path."""
        return self._storage_backend.download_file(key)
    
    def get_file_info(self, key: str) -> dict:
        """Get file metadata."""
        return self._storage_backend.get_file_info(key)
    
    def delete_file(self, key: str) -> bool:
        """Delete a file."""
        return self._storage_backend.delete_file(key)
    
    def file_exists(self, key: str) -> bool:
        """Check if file exists."""
        return self._storage_backend.file_exists(key)
    
    def generate_presigned_url(self, key: str, expiration: int = 3600, method: str = 'get_object') -> str:
        """Generate presigned URL."""
        return self._storage_backend.generate_presigned_url(key, expiration, method)
    
    @property
    def backend_type(self) -> str:
        """Get the current backend type."""
        return self._backend_type
    
    @property
    def bucket_name(self) -> str:
        """Get bucket name (for S3) or storage path (for local)."""
        if self._backend_type == 's3':
            return self._storage_backend.bucket_name
        else:
            return self._storage_backend.media_root


# Global unified storage service instance
storage_service = UnifiedStorageService()
