from django.contrib import admin
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from django import forms
from .models import Business, BusinessUser, BusinessSettings, Location, OnlineBookingRules, AccessLevel, BusinessCustomer, StylistLevel
# TODO: Re-enable when BusinessNotificationSettings import issue is resolved
# from .models import BusinessNotificationSettings


class ToggleWidget(forms.CheckboxInput):
    """Custom toggle switch widget"""

    def __init__(self, attrs=None, description=None):
        self.description = description
        default_attrs = {'class': 'toggle-checkbox'}
        if attrs:
            default_attrs.update(attrs)
        super().__init__(default_attrs)

    def render(self, name, value, attrs=None, renderer=None):
        from django.utils.safestring import mark_safe

        # Get the basic checkbox HTML
        checkbox_html = super().render(name, value, attrs, renderer)

        # Create the toggle switch HTML with escaped braces
        help_html = ''
        if self.description:
            help_html = f'<span class="toggle-help"><span class="toggle-help-icon">?</span><span class="toggle-tooltip">{self.description}</span></span>'

        toggle_html = f'''
        <div class="smart-toggle-row">
            <label class="toggle-switch">
                {checkbox_html}
                <span class="toggle-slider"></span>
            </label>
            {help_html}
        </div>
        <style>
        /* Override Django admin form row styling for smart booking toggles */
        .field-enable_bookend_slots .smart-toggle-row,
        .field-enable_gapless_booking .smart-toggle-row,
        .field-enable_tentative_hold .smart-toggle-row {{
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 8px;
            margin: 0;
            padding: 0;
        }}

        /* Ensure the form row itself is properly aligned */
        .field-enable_bookend_slots,
        .field-enable_gapless_booking,
        .field-enable_tentative_hold {{
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 0;
        }}

        .field-enable_bookend_slots label,
        .field-enable_gapless_booking label,
        .field-enable_tentative_hold label {{
            margin: 0;
            font-weight: 500;
            color: #333;
            flex: 1;
        }}

        .toggle-switch {{
            position: relative;
            display: inline-block;
            width: 60px;
            height: 30px;
            flex-shrink: 0;
        }}
        .toggle-switch input {{
            opacity: 0;
            width: 0;
            height: 0;
        }}
        .toggle-slider {{
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: 0.3s;
            border-radius: 30px;
        }}
        .toggle-slider:before {{
            position: absolute;
            content: "";
            height: 22px;
            width: 22px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: 0.3s;
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }}
        .toggle-switch input:checked + .toggle-slider {{
            background-color: #4CAF50;
        }}
        .toggle-switch input:checked + .toggle-slider:before {{
            transform: translateX(30px);
        }}
        .toggle-switch input:focus + .toggle-slider {{
            box-shadow: 0 0 1px #4CAF50;
        }}
        .toggle-help {{
            position: relative;
            display: inline-block;
            cursor: help;
            flex-shrink: 0;
        }}
        .toggle-help-icon {{
            display: inline-block;
            width: 18px;
            height: 18px;
            background-color: #6c757d;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 18px;
            font-size: 12px;
            font-weight: bold;
        }}
        .toggle-help:hover .toggle-help-icon {{
            background-color: #495057;
        }}
        .toggle-tooltip {{
            visibility: hidden;
            width: 320px;
            background-color: #333;
            color: #fff;
            text-align: left;
            border-radius: 6px;
            padding: 12px 16px;
            position: absolute;
            z-index: 1000;
            bottom: 125%;
            right: 0;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 13px;
            line-height: 1.4;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }}
        .toggle-tooltip::after {{
            content: "";
            position: absolute;
            top: 100%;
            right: 20px;
            border-width: 5px;
            border-style: solid;
            border-color: #333 transparent transparent transparent;
        }}
        .toggle-help:hover .toggle-tooltip {{
            visibility: visible;
            opacity: 1;
        }}
        </style>
        '''

        return mark_safe(toggle_html)


class OnlineBookingRulesForm(forms.ModelForm):
    """Custom form for OnlineBookingRules with smart booking rule toggles"""

    class Meta:
        model = OnlineBookingRules
        fields = '__all__'
        widgets = {
            'enable_bookend_slots': ToggleWidget(
                description='Show only the first and last available time slots of the day per stylist'
            ),
            'enable_gapless_booking': ToggleWidget(
                description='Fill gaps in availability by snapping to actual appointment end times'
            ),
            'enable_tentative_hold': ToggleWidget(
                description='Show partial time slots when remaining time is within tolerance threshold'
            ),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add help text to fields
        self.fields['enable_bookend_slots'].help_text = 'Show only the first and last available time slots of the day per stylist'
        self.fields['enable_gapless_booking'].help_text = 'Fill gaps in availability by snapping to actual appointment end times'
        self.fields['enable_tentative_hold'].help_text = 'Show partial time slots when remaining time is within tolerance threshold'

        # Add JavaScript for conditional tolerance field display
        self.fields['tentative_hold_tolerance'].widget.attrs.update({
            'data-conditional': 'enable_tentative_hold'
        })

    class Media:
        css = {
            'all': ('admin/css/smart_booking_rules.css',)
        }
        js = ('admin/js/smart_booking_rules.js',)


class BusinessSettingsInline(admin.StackedInline):
    """
    This is a proxy/virtual view for OnlineBookingRules.
    """
    model = BusinessSettings
    can_delete = False
    readonly_fields = ('timezone', 'currency', 'booking_lead_time', 'booking_window', 'appointment_interval', 'created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('timezone', 'currency')
        }),
        ('Booking Rules', {
            'fields': ('booking_lead_time', 'booking_window', 'appointment_interval'),
            'description': _('These are reference values - edit them in Online Booking Rules.')
        }),
    )

    def has_add_permission(self, request, obj=None):
        return False

@admin.register(Business)
class BusinessAdmin(admin.ModelAdmin):
    list_display = ('name', 'owner_email', 'is_active', 'get_customer_count', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'owner__email', 'description', 'phone', 'email')
    ordering = ('name',)
    inlines = []  # Remove OnlineBookingRulesInline
    
    def get_queryset(self, request):
        """Optimize queryset to prevent N+1 queries in admin list view"""
        queryset = super().get_queryset(request)
        return queryset.select_related('owner')
    
    fieldsets = (
        (None, {
            'fields': ('name', 'owner', 'description')
        }),
        ('Contact Information', {
            'fields': ('website', 'phone', 'email')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    readonly_fields = ('created_at', 'updated_at')

    def owner_email(self, obj):
        return obj.owner.email if obj.owner else '-'
    owner_email.short_description = 'Owner Email'
    owner_email.admin_order_field = 'owner__email'

    def get_customer_count(self, obj):
        return obj.business_users.count()
    get_customer_count.short_description = 'Users'

@admin.register(BusinessUser)
class BusinessUserAdmin(admin.ModelAdmin):
    list_display = ('user_email', 'business_name', 'access_level', 'is_active', 'is_primary')
    list_filter = ('business', 'access_level', 'is_active', 'is_primary')
    search_fields = ('user__email', 'business__name')
    autocomplete_fields = ['user', 'business']
    
    def get_queryset(self, request):
        """Optimize queryset to prevent N+1 queries in admin list view"""
        queryset = super().get_queryset(request)
        return queryset.select_related('user', 'business', 'access_level')
    
    fieldsets = (
        (None, {
            'fields': ('business', 'user', 'access_level', 'is_active', 'is_primary')
        }),
        (_('Custom Permissions'), {
            'fields': ('custom_permissions',),
            'description': _('Override access level permissions for this user. Leave empty to use access level defaults.')
        }),
    )

    def user_email(self, obj):
        return obj.user.email if obj.user else '-'
    user_email.short_description = _('User Email')
    user_email.admin_order_field = 'user__email'

    def business_name(self, obj):
        return obj.business.name if obj.business else '-'
    business_name.short_description = _('Business')
    business_name.admin_order_field = 'business__name'

@admin.register(BusinessSettings)
class BusinessSettingsAdmin(admin.ModelAdmin):
    """
    Read-only proxy admin for OnlineBookingRules.
    """
    list_display = ('business_name', 'timezone', 'currency', 'booking_lead_time', 'booking_window', 'appointment_interval')
    list_filter = []  # Remove filters that reference non-existent fields
    search_fields = ('business__name',)
    readonly_fields = ('timezone', 'currency', 'booking_lead_time', 'booking_window', 'appointment_interval', 'created_at', 'updated_at')
    
    def get_queryset(self, request):
        """Optimize queryset to prevent N+1 queries in admin list view"""
        queryset = super().get_queryset(request)
        return queryset.select_related('business')
    
    fieldsets = (
        (None, {
            'fields': ('business', 'timezone', 'currency')
        }),
        ('Booking Rules', {
            'fields': ('booking_lead_time', 'booking_window', 'appointment_interval'),
            'description': _('These settings are read-only references to Online Booking Rules. To make changes, please use the Online Booking Rules admin page.')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def business_name(self, obj):
        return obj.business.name if obj.business else '-'
    business_name.short_description = 'Business'
    business_name.admin_order_field = 'business__name'
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
    
    def has_delete_permission(self, request, obj=None):
        return False

@admin.register(Location)
class LocationAdmin(admin.ModelAdmin):
    list_display = ('name', 'business_name', 'city', 'state', 'country', 'status_badge')
    list_filter = ('is_active', 'country', 'state', 'business')
    search_fields = ('name', 'business__name', 'address_line1', 'city', 'state', 'country', 'postal_code')
    ordering = ('business', 'name')
    
    fieldsets = (
        (None, {
            'fields': ('business', 'name', 'is_active')
        }),
        ('Address', {
            'fields': ('address_line1', 'address_line2', 'city', 'state', 'country', 'postal_code')
        }),
        ('Contact', {
            'fields': ('phone', 'email')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    readonly_fields = ('created_at', 'updated_at')

    def business_name(self, obj):
        return obj.business.name if obj.business else '-'
    business_name.short_description = 'Business'
    business_name.admin_order_field = 'business__name'

    def status_badge(self, obj):
        colors = {
            True: 'green',
            False: 'red'
        }
        status = 'Active' if obj.is_active else 'Inactive'
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.is_active, 'gray'),
            status
        )
    status_badge.short_description = 'Status'

@admin.register(OnlineBookingRules)
class OnlineBookingRulesAdmin(admin.ModelAdmin):
    """
    Primary admin for booking settings - this is the source of truth.
    """
    form = OnlineBookingRulesForm
    list_display = ('business', 'max_days_in_advance', 'min_hours_before', 'appointment_interval', 'allow_cancellation', 'require_payment')
    list_filter = ('business', 'allow_cancellation', 'allow_rescheduling', 'require_payment')
    search_fields = ('business__name',)
    
    fieldsets = (
        ('Business Config', {
            'fields': (
                'business', 
                'timezone',
                'currency',
            ),
            'description': _('General business configuration settings')
        }),
        ('Appointment Settings', {
            'fields': (
                'appointment_interval',
                'max_days_in_advance',
                'min_hours_before',
                ('enable_bookend_slots', 'enable_gapless_booking', 'enable_tentative_hold'),
                'tentative_hold_tolerance',
            ),
            'description': _('Configure appointment scheduling rules and smart booking options for online booking')
        }),
        ('Cancellation & Rescheduling', {
            'fields': (
                ('allow_cancellation', 'cancellation_hours_before'),
                'cancellation_policy',
                ('allow_rescheduling', 'rescheduling_hours_before'),
            ),
            'description': _('Configure cancellation and rescheduling policies for online bookings')
        }),
        ('Payment Requirements', {
            'fields': (
                ('require_payment', 'deposit_percentage'),
            )
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    readonly_fields = ('created_at', 'updated_at')

@admin.register(BusinessCustomer)
class BusinessCustomerAdmin(admin.ModelAdmin):
    list_display = ('get_customer_name', 'get_business_name', 'loyalty_points', 'opt_in_marketing', 'email_reminders', 'sms_reminders')
    list_filter = ('business', 'opt_in_marketing', 'email_reminders', 'sms_reminders')
    search_fields = ('customer__user__email', 'customer__user__first_name', 'customer__user__last_name', 'business__name')
    autocomplete_fields = ['business', 'customer']
    filter_horizontal = ('tags',)
    
    def get_queryset(self, request):
        """Optimize queryset to prevent N+1 queries in admin list view"""
        queryset = super().get_queryset(request)
        # OPTIMIZATION: Use select_related to fetch related objects in a single query
        return queryset.select_related(
            'customer',           # BusinessCustomer → CustomerProfile
            'customer__user',     # CustomerProfile → User
            'business'            # BusinessCustomer → Business
        ).prefetch_related('tags')
    
    fieldsets = (
        (_('Relationship'), {
            'fields': ('business', 'customer', 'notes', 'loyalty_points'),
        }),
        (_('Preferences'), {
            'fields': ('opt_in_marketing', 'email_reminders', 'sms_reminders', 'tags'),
        }),
        (_('Business Relationship'), {
            'fields': ('customer_since', 'last_visited', 'membership_type', 'referred_by'),
            'classes': ('collapse',)
        }),
        (_('Business Preferences'), {
            'fields': ('online_booking_allowed', 'credit_card_info'),
            'classes': ('collapse',)
        }),
        (_('Statistics'), {
            'fields': ('appointments_booked', 'classes_booked', 'amount_paid', 'no_shows_cancellations', 'employee_seen'),
            'classes': ('collapse',)
        }),
        (_('Import Metadata'), {
            'fields': ('imported_at', 'import_source'),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    readonly_fields = ('created_at', 'updated_at')
    
    def get_customer_name(self, obj):
        if obj.customer and obj.customer.user:
            return f"{obj.customer.user.first_name} {obj.customer.user.last_name}"
        return '-'
    get_customer_name.short_description = _('Customer')
    get_customer_name.admin_order_field = 'customer__user__last_name'

    def get_business_name(self, obj):
        return obj.business.name if obj.business else '-'
    get_business_name.short_description = _('Business')
    get_business_name.admin_order_field = 'business__name'

@admin.register(StylistLevel)
class StylistLevelAdmin(admin.ModelAdmin):
    list_display = ('name', 'business', 'description', 'level_order', 'is_default', 'is_active')
    list_filter = ('business', 'is_active', 'is_default')
    search_fields = ('name', 'description', 'business__name')
    ordering = ('business', 'level_order')
    autocomplete_fields = ['business']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('business', 'name', 'description', 'is_active')
        }),
        ('Level Settings', {
            'fields': ('level_order', 'is_default')
        }),
    )
    
    def save_model(self, request, obj, form, change):
        """Ensure only one default level per business"""
        if obj.is_default:
            # Reset other defaults for this business
            StylistLevel.objects.filter(
                business=obj.business, 
                is_default=True
            ).exclude(pk=obj.pk).update(is_default=False)
        super().save_model(request, obj, form, change)


# TODO: Re-enable when BusinessNotificationSettings import issue is resolved
# @admin.register(BusinessNotificationSettings)
class BusinessNotificationSettingsAdmin(admin.ModelAdmin):
    list_display = ('business_name', 'appointment_detail_enabled', 'confirmation_request_enabled', 'appointment_reminder_enabled')
    list_filter = ('appointment_detail_enabled', 'confirmation_request_enabled', 'appointment_reminder_enabled')
    search_fields = ('business__name',)
    ordering = ('business__name',)

    fieldsets = (
        (_('Business'), {
            'fields': ('business',)
        }),
        (_('Email & Text Notifications'), {
            'fields': (
                ('appointment_detail_enabled',),
                ('confirmation_request_enabled', 'confirmation_hours_before'),
                ('appointment_reminder_enabled', 'reminder_hours_before'),
            ),
            'description': _('Configure when notifications are sent to customers')
        }),
        (_('Custom Messages'), {
            'fields': (
                'new_appointment_message',
                'required_policy_display',
                'cancellation_no_show_policy_message',
            ),
            'description': _('Custom messages to include in appointment emails')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    readonly_fields = ('created_at', 'updated_at', 'required_policy_display')

    def business_name(self, obj):
        return obj.business.name if obj.business else '-'
    business_name.short_description = _('Business')
    business_name.admin_order_field = 'business__name'

    def required_policy_display(self, obj):
        """Display the required cancellation policy that cannot be edited."""
        from django.utils.html import format_html
        # TODO: Fix when BusinessNotificationSettings import issue is resolved
        policy_text = obj.get_required_cancellation_policy() if obj else "Default policy text"
        return format_html(
            '<div style="background-color: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; margin: 10px 0;">'
            '<h4 style="margin-top: 0; color: #495057;">Required Appointment Cancellation Fee Policy:</h4>'
            '<div style="white-space: pre-line; color: #6c757d; font-size: 14px;">{}</div>'
            '</div>',
            policy_text
        )
    required_policy_display.short_description = _('Required Policy (Not Editable)')
    required_policy_display.allow_tags = True

    def formfield_for_dbfield(self, db_field, request, **kwargs):
        """Customize form fields"""
        if db_field.name in ['appointment_detail_enabled', 'confirmation_request_enabled', 'appointment_reminder_enabled']:
            kwargs['widget'] = ToggleWidget(
                description=db_field.help_text
            )
        elif db_field.name == 'new_appointment_message':
            kwargs['widget'] = forms.Textarea(attrs={'rows': 4, 'cols': 80})
        elif db_field.name == 'cancellation_no_show_policy_message':
            kwargs['widget'] = forms.Textarea(attrs={
                'rows': 6,
                'cols': 80,
                'placeholder': 'Add any additional cancellation and no-show policy details here...'
            })
        return super().formfield_for_dbfield(db_field, request, **kwargs)